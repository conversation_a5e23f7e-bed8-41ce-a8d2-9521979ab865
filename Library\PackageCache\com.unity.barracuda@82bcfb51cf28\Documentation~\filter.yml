apiRules:
  - exclude:
      # Exclude types from Onnx.* namespace
      uidRegex: ^Onnx\..*$
      type: Type

  - exclude:
      # Exclude types from Unity.Barracuda.Editor.* namespace
      uidRegex: ^Unity\.Barracuda\.Editor\..*$
      type: Type

  - exclude:
      # Exclude types from Unity.Barracuda.TensorCachingAllocator type
      uidRegex: ^Unity\.Barracuda\.TensorCachingAllocator$
      type: Type

  - exclude:
      # Exclude types from Unity.Barracuda.ONNX.ONNXNodeWrapper type
      uidRegex: ^Unity\.Barracuda\.ONNX\.ONNXNodeWrapper.*$
      type: Type
