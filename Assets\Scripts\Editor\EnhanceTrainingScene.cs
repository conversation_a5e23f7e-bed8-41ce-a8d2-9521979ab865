using UnityEngine;
using UnityEditor;

/// <summary>
/// Enhances existing training scene with advanced monitoring and optimization tools
/// </summary>
public class EnhanceTrainingScene : EditorWindow
{
    [MenuItem("SquadMate AI/Enhance Training Scene")]
    public static void ShowWindow()
    {
        GetWindow<EnhanceTrainingScene>("Enhance Training Scene");
    }

    private void OnGUI()
    {
        GUILayout.Label("🚀 Training Scene Enhancer", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("Add advanced training tools to your scene:");
        GUILayout.Label("✅ Training Performance Optimizer");
        GUILayout.Label("✅ Advanced Training Analytics");
        GUILayout.Label("✅ Enhanced Debug GUI");
        GUILayout.Label("✅ Real-time Performance Monitoring");
        GUILayout.Label("✅ Data Export & Analysis Tools");

        GUILayout.Space(20);

        if (GUILayout.Button("🔧 ENHANCE SCENE NOW", GUILayout.Height(50)))
        {
            EnhanceCurrentScene();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("📊 Add Analytics Only", GUILayout.Height(30)))
        {
            AddAnalyticsOnly();
        }

        if (GUILayout.Button("⚡ Add Performance Optimizer Only", GUILayout.Height(30)))
        {
            AddOptimizerOnly();
        }

        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This will add advanced monitoring tools to your current scene.", MessageType.Info);
    }

    public static void EnhanceCurrentScene()
    {
        Debug.Log("🚀 Enhancing Training Scene with Advanced Tools...");

        try
        {
            // Add Training Optimizer
            AddTrainingOptimizer();

            // Add Training Analytics
            AddTrainingAnalytics();

            // Enhance existing TrainingDebugger
            EnhanceTrainingDebugger();

            // Configure all components
            ConfigureEnhancements();

            Debug.Log("✅ TRAINING SCENE ENHANCED!");
            Debug.Log("🎯 New Features Added:");
            Debug.Log("   • Performance Optimizer with auto-scaling");
            Debug.Log("   • Advanced Analytics with CSV export");
            Debug.Log("   • Enhanced Debug GUI with metrics");
            Debug.Log("   • Real-time performance monitoring");

            EditorUtility.DisplayDialog("Success!",
                "Training scene enhanced successfully!\n\n" +
                "New Features:\n" +
                "• Performance Optimizer (auto time-scale)\n" +
                "• Training Analytics (CSV export)\n" +
                "• Enhanced Debug GUI\n" +
                "• Real-time monitoring\n\n" +
                "Check the new GUI panels when you press Play!", "Awesome!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error enhancing scene: {e.Message}");
            EditorUtility.DisplayDialog("Error", $"Failed to enhance scene:\n{e.Message}", "OK");
        }
    }

    private static void AddTrainingOptimizer()
    {
        GameObject optimizer = GameObject.Find("TrainingOptimizer");
        if (optimizer == null)
        {
            optimizer = new GameObject("TrainingOptimizer");
            optimizer.AddComponent<TrainingOptimizer>();

            // Configure optimizer
            TrainingOptimizer optimizerComponent = optimizer.GetComponent<TrainingOptimizer>();
            optimizerComponent.autoOptimize = true;
            optimizerComponent.targetFPS = 60f;
            optimizerComponent.maxTimeScale = 20f;
            optimizerComponent.dynamicQuality = true;

            Debug.Log("✅ Added Training Optimizer");
        }
        else
        {
            Debug.Log("⚠️ Training Optimizer already exists");
        }
    }

    private static void AddTrainingAnalytics()
    {
        GameObject analytics = GameObject.Find("TrainingAnalytics");
        if (analytics == null)
        {
            analytics = new GameObject("TrainingAnalytics");
            analytics.AddComponent<TrainingAnalytics>();

            // Configure analytics
            TrainingAnalytics analyticsComponent = analytics.GetComponent<TrainingAnalytics>();
            analyticsComponent.enableAnalytics = true;
            analyticsComponent.saveToFile = true;
            analyticsComponent.dataCollectionInterval = 5f;
            analyticsComponent.maxDataPoints = 1000;

            Debug.Log("✅ Added Training Analytics");
        }
        else
        {
            Debug.Log("⚠️ Training Analytics already exists");
        }
    }

    private static void EnhanceTrainingDebugger()
    {
        GameObject debugger = GameObject.Find("TrainingDebugger");
        if (debugger != null)
        {
            TrainingDebugger debugComponent = debugger.GetComponent<TrainingDebugger>();
            if (debugComponent != null)
            {
                debugComponent.showDebugInfo = true;
                debugComponent.showGUI = true;
                debugComponent.logRewards = true;
                debugComponent.updateInterval = 1f;

                Debug.Log("✅ Enhanced Training Debugger");
            }
        }
        else
        {
            Debug.LogWarning("⚠️ TrainingDebugger not found - creating new one");
            GameObject newDebugger = new GameObject("TrainingDebugger");
            TrainingDebugger debugComponent = newDebugger.AddComponent<TrainingDebugger>();
            debugComponent.showDebugInfo = true;
            debugComponent.showGUI = true;
            debugComponent.logRewards = true;
        }
    }

    private static void ConfigureEnhancements()
    {
        // Find and configure all components
        SquadMateAgent agent = FindObjectOfType<SquadMateAgent>();
        TrainingOptimizer optimizer = FindObjectOfType<TrainingOptimizer>();
        TrainingAnalytics analytics = FindObjectOfType<TrainingAnalytics>();

        if (agent != null && optimizer != null)
        {
            Debug.Log("🔗 Configured component references");
        }

        // Set optimal training settings
        if (Application.isPlaying)
        {
            Time.timeScale = 10f; // Start with 10x speed
            QualitySettings.SetQualityLevel(0); // Lowest quality for training
        }

        Debug.Log("⚙️ Applied optimal training settings");
    }

    public static void AddAnalyticsOnly()
    {
        Debug.Log("📊 Adding Training Analytics...");
        AddTrainingAnalytics();
        Debug.Log("✅ Training Analytics added!");
    }

    public static void AddOptimizerOnly()
    {
        Debug.Log("⚡ Adding Performance Optimizer...");
        AddTrainingOptimizer();
        Debug.Log("✅ Performance Optimizer added!");
    }
}

/// <summary>
/// Quick menu items for training enhancements
/// </summary>
public class TrainingEnhancementMenu
{
    [MenuItem("SquadMate AI/Quick Actions/Add Performance Optimizer")]
    public static void QuickAddOptimizer()
    {
        EnhanceTrainingScene.AddOptimizerOnly();
    }

    [MenuItem("SquadMate AI/Quick Actions/Add Training Analytics")]
    public static void QuickAddAnalytics()
    {
        EnhanceTrainingScene.AddAnalyticsOnly();
    }

    [MenuItem("SquadMate AI/Quick Actions/Set Training Speed 10x")]
    public static void SetTrainingSpeed10x()
    {
        Time.timeScale = 10f;
        Debug.Log("⚡ Time scale set to 10x");
    }

    [MenuItem("SquadMate AI/Quick Actions/Set Training Speed 20x")]
    public static void SetTrainingSpeed20x()
    {
        Time.timeScale = 20f;
        Debug.Log("⚡ Time scale set to 20x");
    }

    [MenuItem("SquadMate AI/Quick Actions/Reset Speed to 1x")]
    public static void ResetSpeed()
    {
        Time.timeScale = 1f;
        Debug.Log("⏰ Time scale reset to 1x");
    }

    [MenuItem("SquadMate AI/Quick Actions/Export Training Data")]
    public static void ExportTrainingData()
    {
        TrainingAnalytics analytics = FindObjectOfType<TrainingAnalytics>();
        if (analytics != null)
        {
            analytics.ExportAnalytics();
            Debug.Log("📊 Training data exported!");
        }
        else
        {
            Debug.LogWarning("⚠️ Training Analytics not found in scene");
        }
    }
}
