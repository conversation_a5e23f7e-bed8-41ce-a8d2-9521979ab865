{"name": "Unity.ML-Agents.Editor", "references": ["Unity.ML-Agents", "Unity.Barracuda", "Unity.ML-Agents.CommunicatorObjects"], "optionalUnityReferences": [], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "MLA_UNITY_PHYSICS_MODULE"}, {"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "MLA_UNITY_PHYSICS2D_MODULE"}]}