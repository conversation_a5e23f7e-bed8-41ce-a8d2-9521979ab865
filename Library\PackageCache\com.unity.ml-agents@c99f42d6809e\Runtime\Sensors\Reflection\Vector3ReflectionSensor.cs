namespace Unity.MLAgents.Sensors.Reflection
{
    /// <summary>
    /// Sensor that wraps a Vector3 field or property of an object, and returns
    /// that as an observation.
    /// </summary>
    internal class Vector3ReflectionSensor : ReflectionSensorBase
    {
        public Vector3ReflectionSensor(ReflectionSensorInfo reflectionSensorInfo)
            : base(reflectionSensorInfo, 3)
        { }

        internal override void WriteReflected<PERSON>ield(ObservationWriter writer)
        {
            var vecVal = (UnityEngine.Vector3)GetReflectedValue();
            writer.Add(vecVal);
        }
    }
}
