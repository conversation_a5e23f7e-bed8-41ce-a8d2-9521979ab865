{"count": 1, "self": 52.298303999999995, "total": 52.3188651, "children": {"InitializeActuators": {"count": 1, "self": 0.0035526999999999998, "total": 0.0035526999999999998, "children": null}, "InitializeSensors": {"count": 1, "self": 0.0030463, "total": 0.0030463, "children": null}, "AgentSendState": {"count": 2317, "self": 0.0019951, "total": 0.0019951, "children": null}, "DecideAction": {"count": 2317, "self": 0.0069765, "total": 0.0069765, "children": null}, "AgentAct": {"count": 2317, "self": 0.0039929, "total": 0.0039929, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}