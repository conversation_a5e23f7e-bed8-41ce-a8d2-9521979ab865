{"count": 1, "self": 299.8171392, "total": 299.8819549, "children": {"InitializeActuators": {"count": 1, "self": 0.0020015, "total": 0.0020015, "children": null}, "InitializeSensors": {"count": 1, "self": 0.0019473, "total": 0.0019473, "children": null}, "AgentSendState": {"count": 3969, "self": 0.01092, "total": 0.01092, "children": null}, "DecideAction": {"count": 3969, "self": 0.042932899999999996, "total": 0.042932899999999996, "children": null}, "AgentAct": {"count": 3969, "self": 0.006018, "total": 0.006018, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}