{"count": 1, "self": 7397.6840192, "total": 7416.0704791, "children": {"InitializeActuators": {"count": 1, "self": 0.0020012999999999997, "total": 0.0020012999999999997, "children": null}, "InitializeSensors": {"count": 1, "self": 0.0020227, "total": 0.0020227, "children": null}, "AgentSendState": {"count": 3859082, "self": 4.0793056, "total": 4.0793055, "children": null}, "DecideAction": {"count": 3859082, "self": 12.4100784, "total": 12.4100787, "children": null}, "AgentAct": {"count": 3859082, "self": 1.8919412, "total": 1.8919411, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}