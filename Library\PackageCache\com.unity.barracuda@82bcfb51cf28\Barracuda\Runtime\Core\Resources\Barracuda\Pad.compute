#pragma kernel Border2D_NHWC CHANNELS_FIRST=0
#pragma kernel Border2D_NCHW CHANNELS_FIRST=1
#pragma kernel Pad2DEdge_NHWC CHANNELS_FIRST=0
#pragma kernel Pad2DEdge_NCHW CHANNELS_FIRST=1
#pragma kernel Pad2DReflect_NHWC CHANNELS_FIRST=0
#pragma kernel Pad2DReflect_NCHW CHANNELS_FIRST=1
#pragma kernel Pad2DSymmetric_NHWC CHANNELS_FIRST=0
#pragma kernel Pad2DSymmetric_NCHW CHANNELS_FIRST=1

#include "Tensor.cginc"

TENSOR_DECL(X)
TENSOR_DECL(B)
TENSOR_DECL_RW(O)

uint4 _Pool;
uint4 _Stride;
uint4 _Pad;
float _Beta;

NUMTHREADS((4, 8, 8), (4, 8, 4), (4, 4, 4))
void KERNEL_FUNC(Border2D)(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.channels, O.width, O.height);
    TENSOR_ARGS2(X, O);

    uint c = dispatchThreadID.x;
    uint x = dispatchThreadID.y;
    uint y = dispatchThreadID.z;

    if (c >= O.channels) return;
    if (x >= O.width) return;
    if (y >= O.height) return;

    // NOTE: negative "pad" variable crop X tensor
    int croppedWidth = _Pool.x;
    int croppedHeight = _Pool.y;
	int croppedChannels = _Pool.z;

    int readX = x - _Pad.x;
    int readY = y - _Pad.y;
	int readC = c - _Pad.z;

    bool paddedTexel = (readX < 0 || readX >= croppedWidth || readY < 0 || readY >= croppedHeight || readC < 0 || readC >= croppedChannels);

    for (uint n = 0; n < O.batch; ++n)
    {
        float v = _Beta;

        if (!paddedTexel)
            v = X.Get(n, readY, readX, readC);

        O.Set(n, y, x, c, v);
    }
}

void ClampHWToTensorShape(uint2 shape, inout int height, inout int width)
{
    width = clamp(width, 0, (int)shape.x - 1);
    height = clamp(height, 0, (int)shape.y - 1);
}

NUMTHREADS((4, 8, 8), (4, 8, 4), (4, 4, 4))
void KERNEL_FUNC(Pad2DEdge)(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.channels, O.width, O.height);
    TENSOR_ARGS2(X, O);

    uint c = dispatchThreadID.x;
    uint x = dispatchThreadID.y;
    uint y = dispatchThreadID.z;

    if (c >= O.channels) return;
    if (x >= O.width) return;
    if (y >= O.height) return;

    int readX = x - _Pad.x;
    int readY = y - _Pad.y;

    //clamp read indices to source
    ClampHWToTensorShape(uint2(X.width, X.height), readY, readX);

    for (uint n = 0; n < O.batch; ++n)
    {
        float v = X.Get(n, readY, readX, c);
        O.Set(n, y, x, c, v);
    }
}

NUMTHREADS((4, 8, 8), (4, 8, 4), (4, 4, 4))
void KERNEL_FUNC(Pad2DReflect)(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.channels, O.width, O.height);
    TENSOR_ARGS2(X, O);

    uint c = dispatchThreadID.x;
    uint x = dispatchThreadID.y;
    uint y = dispatchThreadID.z;

    if (c >= O.channels) return;
    if (x >= O.width) return;
    if (y >= O.height) return;

    int readX = x - _Pad.x;
    int readY = y - _Pad.y;

    uint2 Xshape = uint2(X.width, X.height);

    int lastXIndex = Xshape.x - 1;
    int lastYIndex = Xshape.y - 1;

    //x reflect indexing
    readX = (readX < 0) ? -readX : readX;
    readX = (readX > lastXIndex) ? lastXIndex - (readX - lastXIndex) : readX;
    //y reflect indexing
    readY = (readY < 0) ? -readY : readY;
    readY = (readY > lastYIndex) ? lastYIndex - (readY - lastYIndex) : readY;

    //clamp read indices to source
    ClampHWToTensorShape(Xshape, readY, readX);

    for (uint n = 0; n < O.batch; ++n)
    {
        float v = X.Get(n, readY, readX, c);
        O.Set(n, y, x, c, v);
    }
}

NUMTHREADS((4, 8, 8), (4, 8, 4), (4, 4, 4))
void KERNEL_FUNC(Pad2DSymmetric)(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.channels, O.width, O.height);
    TENSOR_ARGS2(X, O);

    uint c = dispatchThreadID.x;
    uint x = dispatchThreadID.y;
    uint y = dispatchThreadID.z;

    if (c >= O.channels) return;
    if (x >= O.width) return;
    if (y >= O.height) return;

    int readX = x - _Pad.x;
    int readY = y - _Pad.y;

    uint2 Xshape = uint2(X.width, X.height);

    int lastXIndex = Xshape.x - 1;
    int lastYIndex = Xshape.y - 1;

    //x reflect indexing
    readX = (readX < 0) ? -readX - 1: readX;
    readX = (readX > lastXIndex) ? lastXIndex - (readX - lastXIndex) + 1: readX;
    //y reflect indexing
    readY = (readY < 0) ? -readY - 1: readY;
    readY = (readY > lastYIndex) ? lastYIndex - (readY - lastYIndex) + 1: readY;

    //clamp read indices to source
    ClampHWToTensorShape(Xshape, readY, readX);

    for (uint n = 0; n < O.batch; ++n)
    {
        float v = X.Get(n, readY, readX, c);
        O.Set(n, y, x, c, v);
    }
}
