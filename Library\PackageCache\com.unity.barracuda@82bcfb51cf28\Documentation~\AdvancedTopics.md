# Advanced topics

This section provides more information on the following topics:

* [Supported operators](SupportedOperators.md): provides an overview of supported operators
* [Supported platforms](SupportedPlatforms.md): provides an overview of supported platforms
* [Supported architectures](SupportedArchitectures.md): provides an overview of supported architectures
* [Exporting model to ONNX](Exporting.md): explains how to export a network to ONNX
* [Loading model](Loading.md): explains how to load a ONNX network to Barracuda
* [Using IWorker interface](Worker.md): explains how to run your model on different backends
* [Model execution](ModelExecution.md): explains how to run a model
* [Model outputs](ModelOutput.md): explains how to introspect the model and query outputs
* [Tensors: handling data](TensorHandling.md): explains how to handle data in Barracuda
* [Memory management](MemoryManagement.md): explains how memory is managed in Barracuda 