using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;
using System.IO;

/// <summary>
/// Export and test trained ML-Agents models
/// </summary>
public class ModelExportTester : EditorWindow
{
    private string modelPath = "";
    private bool isTestingMode = false;
    private SquadMateAgent agent;
    private BehaviorParameters behaviorParams;
    
    [MenuItem("SquadMate AI/Model Export & Testing")]
    public static void ShowWindow()
    {
        GetWindow<ModelExportTester>("Model Export & Testing");
    }

    private void OnGUI()
    {
        GUILayout.Label("🤖 Model Export & Testing", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // Current model status
        agent = FindObjectOfType<SquadMateAgent>();
        if (agent != null)
        {
            behaviorParams = agent.GetComponent<BehaviorParameters>();
        }

        if (behaviorParams != null)
        {
            GUILayout.Label("📊 Current Model Status:", EditorStyles.boldLabel);
            GUILayout.Label($"Behavior: {behaviorParams.BehaviorName}");
            GUILayout.Label($"Type: {behaviorParams.BehaviorType}");
            GUILayout.Label($"Model: {(behaviorParams.Model != null ? behaviorParams.Model.name : "None")}");
            GUILayout.Space(10);
        }

        // Training mode controls
        GUILayout.Label("🧠 Training Mode Controls:", EditorStyles.boldLabel);
        
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("🎯 Training Mode"))
        {
            SetTrainingMode();
        }
        if (GUILayout.Button("🤖 Inference Mode"))
        {
            SetInferenceMode();
        }
        if (GUILayout.Button("🎮 Heuristic Mode"))
        {
            SetHeuristicMode();
        }
        GUILayout.EndHorizontal();
        
        GUILayout.Space(10);

        // Model loading
        GUILayout.Label("📁 Load Trained Model:", EditorStyles.boldLabel);
        
        GUILayout.BeginHorizontal();
        modelPath = EditorGUILayout.TextField("Model Path:", modelPath);
        if (GUILayout.Button("Browse", GUILayout.Width(60)))
        {
            string path = EditorUtility.OpenFilePanel("Select ONNX Model", Application.dataPath, "onnx");
            if (!string.IsNullOrEmpty(path))
            {
                modelPath = path;
            }
        }
        GUILayout.EndHorizontal();

        if (GUILayout.Button("🔄 Load Model"))
        {
            LoadModel();
        }

        GUILayout.Space(10);

        // Quick model search
        GUILayout.Label("🔍 Quick Model Search:", EditorStyles.boldLabel);
        if (GUILayout.Button("🔍 Find Models in Project"))
        {
            FindModelsInProject();
        }

        GUILayout.Space(10);

        // Testing controls
        GUILayout.Label("🧪 Testing Controls:", EditorStyles.boldLabel);
        
        if (GUILayout.Button("▶️ Start Performance Test"))
        {
            StartPerformanceTest();
        }
        
        if (GUILayout.Button("📊 Compare Before/After"))
        {
            ComparePerformance();
        }

        if (GUILayout.Button("💾 Save Current Behavior"))
        {
            SaveCurrentBehavior();
        }

        GUILayout.Space(10);

        // Export options
        GUILayout.Label("📦 Export Options:", EditorStyles.boldLabel);
        
        if (GUILayout.Button("📤 Export Training Data"))
        {
            ExportTrainingData();
        }

        if (GUILayout.Button("📋 Generate Training Report"))
        {
            GenerateTrainingReport();
        }

        GUILayout.Space(10);

        EditorGUILayout.HelpBox("Use Training Mode for learning, Inference Mode for testing trained models.", MessageType.Info);
    }

    private void SetTrainingMode()
    {
        if (behaviorParams != null)
        {
            behaviorParams.BehaviorType = BehaviorType.Default;
            behaviorParams.Model = null;
            Debug.Log("🧠 Switched to Training Mode");
            
            // Optimize for training
            Time.timeScale = 10f;
            QualitySettings.SetQualityLevel(0);
        }
    }

    private void SetInferenceMode()
    {
        if (behaviorParams != null)
        {
            behaviorParams.BehaviorType = BehaviorType.InferenceOnly;
            Debug.Log("🤖 Switched to Inference Mode");
            
            // Restore normal settings
            Time.timeScale = 1f;
            QualitySettings.SetQualityLevel(5);
        }
    }

    private void SetHeuristicMode()
    {
        if (behaviorParams != null)
        {
            behaviorParams.BehaviorType = BehaviorType.HeuristicOnly;
            Debug.Log("🎮 Switched to Heuristic Mode (Manual Control)");
            
            // Normal settings
            Time.timeScale = 1f;
            QualitySettings.SetQualityLevel(5);
        }
    }

    private void LoadModel()
    {
        if (string.IsNullOrEmpty(modelPath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a model file first.", "OK");
            return;
        }

        if (!File.Exists(modelPath))
        {
            EditorUtility.DisplayDialog("Error", "Model file not found!", "OK");
            return;
        }

        // Copy model to Assets folder if it's not already there
        string fileName = Path.GetFileName(modelPath);
        string destPath = Path.Combine(Application.dataPath, "Models", fileName);
        
        // Create Models folder if it doesn't exist
        string modelsDir = Path.Combine(Application.dataPath, "Models");
        if (!Directory.Exists(modelsDir))
        {
            Directory.CreateDirectory(modelsDir);
        }

        File.Copy(modelPath, destPath, true);
        AssetDatabase.Refresh();

        // Load the model
        string assetPath = "Assets/Models/" + fileName;
        var model = AssetDatabase.LoadAssetAtPath<NNModel>(assetPath);
        
        if (model != null && behaviorParams != null)
        {
            behaviorParams.Model = model;
            SetInferenceMode();
            Debug.Log($"✅ Model loaded: {fileName}");
            EditorUtility.DisplayDialog("Success", $"Model loaded successfully!\n{fileName}", "OK");
        }
        else
        {
            Debug.LogError("❌ Failed to load model");
            EditorUtility.DisplayDialog("Error", "Failed to load model. Make sure it's a valid ONNX file.", "OK");
        }
    }

    private void FindModelsInProject()
    {
        string[] guids = AssetDatabase.FindAssets("t:NNModel");
        
        if (guids.Length == 0)
        {
            Debug.Log("🔍 No ONNX models found in project");
            EditorUtility.DisplayDialog("No Models Found", "No ONNX models found in the project.\n\nTrain a model first or import one.", "OK");
            return;
        }

        Debug.Log($"🔍 Found {guids.Length} model(s) in project:");
        foreach (string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            Debug.Log($"   📁 {path}");
        }

        // Auto-load the first model found
        if (guids.Length > 0)
        {
            string firstModelPath = AssetDatabase.GUIDToAssetPath(guids[0]);
            var model = AssetDatabase.LoadAssetAtPath<NNModel>(firstModelPath);
            
            if (model != null && behaviorParams != null)
            {
                behaviorParams.Model = model;
                SetInferenceMode();
                Debug.Log($"✅ Auto-loaded: {model.name}");
            }
        }
    }

    private void StartPerformanceTest()
    {
        if (!Application.isPlaying)
        {
            EditorUtility.DisplayDialog("Not Playing", "Please enter Play mode first to run performance tests.", "OK");
            return;
        }

        Debug.Log("🧪 Starting Performance Test...");
        Debug.Log("📊 Monitor the agent's behavior for 60 seconds");
        Debug.Log("📈 Check Console for performance metrics");
        
        // Start a coroutine to monitor performance
        if (agent != null)
        {
            agent.StartCoroutine(PerformanceTestCoroutine());
        }
    }

    private System.Collections.IEnumerator PerformanceTestCoroutine()
    {
        float startTime = Time.time;
        float testDuration = 60f;
        
        Vector3 startPos = agent.transform.position;
        float totalReward = 0f;
        int actionCount = 0;
        
        while (Time.time - startTime < testDuration)
        {
            totalReward += agent.GetCumulativeReward();
            actionCount++;
            yield return new WaitForSeconds(1f);
        }
        
        float endTime = Time.time;
        Vector3 endPos = agent.transform.position;
        float distance = Vector3.Distance(startPos, endPos);
        
        Debug.Log("🧪 Performance Test Results:");
        Debug.Log($"   ⏱️ Duration: {testDuration}s");
        Debug.Log($"   💰 Total Reward: {totalReward:F2}");
        Debug.Log($"   📊 Avg Reward/sec: {totalReward/testDuration:F3}");
        Debug.Log($"   🏃 Distance Moved: {distance:F1}m");
        Debug.Log($"   ⚡ Actions/sec: {actionCount/testDuration:F1}");
    }

    private void ComparePerformance()
    {
        Debug.Log("📊 Performance Comparison Feature");
        Debug.Log("💡 To compare performance:");
        Debug.Log("   1. Run test in Training Mode");
        Debug.Log("   2. Load trained model");
        Debug.Log("   3. Run test in Inference Mode");
        Debug.Log("   4. Compare the results");
    }

    private void SaveCurrentBehavior()
    {
        if (behaviorParams != null)
        {
            string configData = $"Behavior: {behaviorParams.BehaviorName}\n" +
                              $"Type: {behaviorParams.BehaviorType}\n" +
                              $"Model: {(behaviorParams.Model != null ? behaviorParams.Model.name : "None")}\n" +
                              $"Timestamp: {System.DateTime.Now}\n";
            
            string path = Path.Combine(Application.persistentDataPath, "behavior_config.txt");
            File.WriteAllText(path, configData);
            
            Debug.Log($"💾 Behavior configuration saved to: {path}");
        }
    }

    private void ExportTrainingData()
    {
        TrainingAnalytics analytics = FindObjectOfType<TrainingAnalytics>();
        if (analytics != null)
        {
            analytics.ExportAnalytics();
            Debug.Log("📤 Training data exported!");
        }
        else
        {
            Debug.LogWarning("⚠️ Training Analytics not found. Add it to the scene first.");
        }
    }

    private void GenerateTrainingReport()
    {
        string report = "📋 SQUADMATE AI TRAINING REPORT\n";
        report += "=" * 50 + "\n";
        report += $"Generated: {System.DateTime.Now}\n\n";
        
        if (behaviorParams != null)
        {
            report += $"Current Behavior: {behaviorParams.BehaviorName}\n";
            report += $"Mode: {behaviorParams.BehaviorType}\n";
            report += $"Model: {(behaviorParams.Model != null ? behaviorParams.Model.name : "None")}\n\n";
        }
        
        report += "🎯 Training Recommendations:\n";
        report += "• Train for 2-4 hours for basic behavior\n";
        report += "• Use 10-20x time scale for faster training\n";
        report += "• Monitor reward trends in TensorBoard\n";
        report += "• Test model periodically in Inference mode\n\n";
        
        string path = Path.Combine(Application.persistentDataPath, "training_report.txt");
        File.WriteAllText(path, report);
        
        Debug.Log($"📋 Training report generated: {path}");
        EditorUtility.DisplayDialog("Report Generated", $"Training report saved to:\n{path}", "OK");
    }
}
