# Supported neural architectures and models

Barracuda currently supports the following neural architectures and models: 

- All [ML-Agents](https://github.com/Unity-Technologies/ml-agents) models
- [MobileNet v1/v2](https://github.com/tensorflow/models/blob/master/research/slim/nets/mobilenet_v1.md) image classifiers
- [Tiny YOLO v2](https://pjreddie.com/darknet/yolov2/) object detector
- U-Net models
- Fully convolutional models
- Fully dense models
- [Spade](https://nvlabs.github.io/SPADE/) architecture
