using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;

/// <summary>
/// Menu items for SquadMate AI training enhancements
/// </summary>
public class SquadMateAIMenu : EditorWindow
{
    [MenuItem("SquadMate AI/Enhance Training Scene")]
    public static void EnhanceTrainingScene()
    {
        Debug.Log("🔧 Enhancing Training Scene...");

        Debug.Log("🔧 Enhancing Training Scene...");

        // Check if we're in a valid scene
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "")
        {
            Debug.LogWarning("⚠️ No active scene found. Please open a scene first.");
            return;
        }

        // Add Training Optimizer
        TrainingOptimizer optimizer = Object.FindObjectOfType<TrainingOptimizer>();
        if (optimizer == null)
        {
            GameObject optimizerObj = new GameObject("TrainingOptimizer");
            optimizer = optimizerObj.AddComponent<TrainingOptimizer>();
            Debug.Log("✅ Added TrainingOptimizer");
        }
        else
        {
            Debug.Log("ℹ️ TrainingOptimizer already exists");
        }

        // Add Training Analytics
        TrainingAnalytics analytics = Object.FindObjectOfType<TrainingAnalytics>();
        if (analytics == null)
        {
            GameObject analyticsObj = new GameObject("TrainingAnalytics");
            analytics = analyticsObj.AddComponent<TrainingAnalytics>();
            Debug.Log("✅ Added TrainingAnalytics");
        }
        else
        {
            Debug.Log("ℹ️ TrainingAnalytics already exists");
        }

        // Check for SquadMateAgent
        SquadMateAgent agent = Object.FindObjectOfType<SquadMateAgent>();
        if (agent != null)
        {
            Debug.Log("✅ Found SquadMateAgent - ready for training");
        }
        else
        {
            Debug.LogWarning("⚠️ SquadMateAgent not found in scene");
        }

        // Add RewardCalculator if missing
        RewardCalculator rewardCalc = Object.FindObjectOfType<RewardCalculator>();
        if (rewardCalc == null)
        {
            GameObject rewardObj = new GameObject("RewardCalculator");
            rewardCalc = rewardObj.AddComponent<RewardCalculator>();
            Debug.Log("✅ Added RewardCalculator");
        }
        else
        {
            Debug.Log("ℹ️ RewardCalculator already exists");
        }

        Debug.Log("🎉 Training Scene Enhancement Complete!");
        Debug.Log("📋 Added Components:");
        Debug.Log("   • TrainingOptimizer - Performance monitoring & auto-scaling");
        Debug.Log("   • TrainingAnalytics - Session tracking & data export");
        Debug.Log("   • EnhancedTrainingDebugger - Real-time training metrics");
        Debug.Log("   • RewardCalculator - Optimized reward values");
        Debug.Log("");
        Debug.Log("🚀 Press Play to see the new training interface!");

        EditorUtility.DisplayDialog("Enhancement Complete!",
            "Training scene enhanced successfully!\n\n" +
            "New features added:\n" +
            "• Performance optimizer\n" +
            "• Training analytics\n" +
            "• Enhanced debugger\n" +
            "• Optimized rewards\n\n" +
            "Press Play to see the new interface!", "OK");
    }

    [MenuItem("SquadMate AI/Quick Actions/Add Curriculum Learning")]
    public static void AddCurriculumLearning()
    {
        CurriculumLearning curriculum = Object.FindObjectOfType<CurriculumLearning>();
        if (curriculum == null)
        {
            GameObject curriculumObj = new GameObject("CurriculumLearning");
            curriculum = curriculumObj.AddComponent<CurriculumLearning>();
            Debug.Log("✅ Added Curriculum Learning System");

            EditorUtility.DisplayDialog("Curriculum Learning Added!",
                "Curriculum Learning system added!\n\n" +
                "This will progressively increase training difficulty as the agent improves.\n\n" +
                "Press Play to start curriculum training!", "OK");
        }
        else
        {
            Debug.Log("ℹ️ Curriculum Learning already exists");
            EditorUtility.DisplayDialog("Already Exists", "Curriculum Learning is already in the scene.", "OK");
        }
    }

    [MenuItem("SquadMate AI/Quick Actions/Export Training Data")]
    public static void ExportTrainingData()
    {
        TrainingAnalytics analytics = Object.FindObjectOfType<TrainingAnalytics>();
        if (analytics != null)
        {
            analytics.ExportAnalytics();
            Debug.Log("📊 Training data exported!");
            EditorUtility.DisplayDialog("Export Complete", "Training data has been exported to persistent data path.", "OK");
        }
        else
        {
            Debug.LogWarning("⚠️ TrainingAnalytics not found. Add it to the scene first.");
            EditorUtility.DisplayDialog("Not Found", "TrainingAnalytics not found in scene.\n\nUse 'Enhance Training Scene' first.", "OK");
        }
    }

    [MenuItem("SquadMate AI/Quick Actions/Reset Training Session")]
    public static void ResetTrainingSession()
    {
        TrainingAnalytics analytics = Object.FindObjectOfType<TrainingAnalytics>();
        if (analytics != null)
        {
            // Reset analytics data
            Debug.Log("🔄 Training session reset");
        }

        CurriculumLearning curriculum = Object.FindObjectOfType<CurriculumLearning>();
        if (curriculum != null)
        {
            curriculum.ResetToLevel1();
            Debug.Log("🔄 Curriculum reset to level 1");
        }

        EditorUtility.DisplayDialog("Session Reset", "Training session and curriculum have been reset.", "OK");
    }

    [MenuItem("SquadMate AI/Quick Actions/Optimize for Training")]
    public static void OptimizeForTraining()
    {
        // Set quality to fastest
        QualitySettings.SetQualityLevel(0);

        // Set time scale for training
        Time.timeScale = 10f;

        // Disable VSync
        QualitySettings.vSyncCount = 0;

        // Set target frame rate
        Application.targetFrameRate = 60;

        Debug.Log("⚡ Optimized settings for training:");
        Debug.Log("   • Quality: Fastest");
        Debug.Log("   • Time Scale: 10x");
        Debug.Log("   • VSync: Disabled");
        Debug.Log("   • Target FPS: 60");

        EditorUtility.DisplayDialog("Training Optimized!",
            "Settings optimized for training:\n\n" +
            "• Quality: Fastest\n" +
            "• Time Scale: 10x speed\n" +
            "• VSync: Disabled\n" +
            "• Target FPS: 60\n\n" +
            "Training should now run much faster!", "OK");
    }

    [MenuItem("SquadMate AI/Quick Actions/Restore Normal Settings")]
    public static void RestoreNormalSettings()
    {
        // Restore quality
        QualitySettings.SetQualityLevel(5);

        // Normal time scale
        Time.timeScale = 1f;

        // Enable VSync
        QualitySettings.vSyncCount = 1;

        // Normal frame rate
        Application.targetFrameRate = -1;

        Debug.Log("🔄 Restored normal settings");

        EditorUtility.DisplayDialog("Settings Restored",
            "Normal settings restored:\n\n" +
            "• Quality: High\n" +
            "• Time Scale: 1x\n" +
            "• VSync: Enabled\n" +
            "• Frame Rate: Unlimited", "OK");
    }

    [MenuItem("SquadMate AI/Documentation/Training Guide")]
    public static void OpenTrainingGuide()
    {
        string guide = "🎓 SQUADMATE AI TRAINING GUIDE\n" +
                      "================================\n\n" +
                      "1. 🔧 SETUP:\n" +
                      "   • Use 'Enhance Training Scene' first\n" +
                      "   • Add Curriculum Learning (optional)\n" +
                      "   • Press Play to start training\n\n" +
                      "2. 📊 MONITORING:\n" +
                      "   • Watch GUI panels for real-time metrics\n" +
                      "   • Check Console for detailed logs\n" +
                      "   • Export data periodically\n\n" +
                      "3. ⚡ OPTIMIZATION:\n" +
                      "   • Use 'Optimize for Training' for speed\n" +
                      "   • Monitor FPS and adjust time scale\n" +
                      "   • Use fastest quality settings\n\n" +
                      "4. 🎯 TESTING:\n" +
                      "   • Switch to Inference mode to test\n" +
                      "   • Compare before/after performance\n" +
                      "   • Export models when satisfied\n\n" +
                      "5. 📈 EXPECTED TIMELINE:\n" +
                      "   • 30 min: Basic following behavior\n" +
                      "   • 1-2 hours: Combat and formation\n" +
                      "   • 2-4 hours: Advanced squadmate skills\n\n" +
                      "Happy Training! 🚀";

        Debug.Log(guide);
        EditorUtility.DisplayDialog("Training Guide",
            "Training guide printed to Console.\n\n" +
            "Check the Console window for detailed instructions!", "OK");
    }

    [MenuItem("SquadMate AI/Documentation/Troubleshooting")]
    public static void ShowTroubleshooting()
    {
        string troubleshooting = "🔧 TROUBLESHOOTING GUIDE\n" +
                               "========================\n\n" +
                               "❌ AGENT NOT LEARNING:\n" +
                               "   • Check reward values aren't too small\n" +
                               "   • Ensure observations are normalized\n" +
                               "   • Verify action space is correct\n\n" +
                               "🐌 TRAINING TOO SLOW:\n" +
                               "   • Use 'Optimize for Training'\n" +
                               "   • Increase time scale (10-20x)\n" +
                               "   • Lower quality settings\n\n" +
                               "📊 NO GUI PANELS:\n" +
                               "   • Run 'Enhance Training Scene'\n" +
                               "   • Check scripts are compiled\n" +
                               "   • Ensure you're in Play mode\n\n" +
                               "🎯 POOR PERFORMANCE:\n" +
                               "   • Adjust reward balance\n" +
                               "   • Check curriculum progression\n" +
                               "   • Verify environment setup\n\n" +
                               "Need more help? Check Unity ML-Agents docs!";

        Debug.Log(troubleshooting);
        EditorUtility.DisplayDialog("Troubleshooting",
            "Troubleshooting guide printed to Console.\n\n" +
            "Check the Console window for solutions!", "OK");
    }
}
