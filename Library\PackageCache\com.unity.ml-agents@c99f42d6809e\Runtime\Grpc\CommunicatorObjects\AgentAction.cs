// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: mlagents_envs/communicator_objects/agent_action.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Unity.MLAgents.CommunicatorObjects {

  /// <summary>Holder for reflection information generated from mlagents_envs/communicator_objects/agent_action.proto</summary>
  internal static partial class AgentActionReflection {

    #region Descriptor
    /// <summary>File descriptor for mlagents_envs/communicator_objects/agent_action.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static AgentActionReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CjVtbGFnZW50c19lbnZzL2NvbW11bmljYXRvcl9vYmplY3RzL2FnZW50X2Fj",
            "dGlvbi5wcm90bxIUY29tbXVuaWNhdG9yX29iamVjdHMijAEKEEFnZW50QWN0",
            "aW9uUHJvdG8SIQoZdmVjdG9yX2FjdGlvbnNfZGVwcmVjYXRlZBgBIAMoAhIN",
            "CgV2YWx1ZRgEIAEoAhIaChJjb250aW51b3VzX2FjdGlvbnMYBiADKAISGAoQ",
            "ZGlzY3JldGVfYWN0aW9ucxgHIAMoBUoECAIQA0oECAMQBEoECAUQBkIlqgIi",
            "VW5pdHkuTUxBZ2VudHMuQ29tbXVuaWNhdG9yT2JqZWN0c2IGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Unity.MLAgents.CommunicatorObjects.AgentActionProto), global::Unity.MLAgents.CommunicatorObjects.AgentActionProto.Parser, new[]{ "VectorActionsDeprecated", "Value", "ContinuousActions", "DiscreteActions" }, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  internal sealed partial class AgentActionProto : pb::IMessage<AgentActionProto> {
    private static readonly pb::MessageParser<AgentActionProto> _parser = new pb::MessageParser<AgentActionProto>(() => new AgentActionProto());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<AgentActionProto> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Unity.MLAgents.CommunicatorObjects.AgentActionReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AgentActionProto() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AgentActionProto(AgentActionProto other) : this() {
      vectorActionsDeprecated_ = other.vectorActionsDeprecated_.Clone();
      value_ = other.value_;
      continuousActions_ = other.continuousActions_.Clone();
      discreteActions_ = other.discreteActions_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public AgentActionProto Clone() {
      return new AgentActionProto(this);
    }

    /// <summary>Field number for the "vector_actions_deprecated" field.</summary>
    public const int VectorActionsDeprecatedFieldNumber = 1;
    private static readonly pb::FieldCodec<float> _repeated_vectorActionsDeprecated_codec
        = pb::FieldCodec.ForFloat(10);
    private readonly pbc::RepeatedField<float> vectorActionsDeprecated_ = new pbc::RepeatedField<float>();
    /// <summary>
    /// mark as deprecated in communicator v1.3.0
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<float> VectorActionsDeprecated {
      get { return vectorActionsDeprecated_; }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 4;
    private float value_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public float Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    /// <summary>Field number for the "continuous_actions" field.</summary>
    public const int ContinuousActionsFieldNumber = 6;
    private static readonly pb::FieldCodec<float> _repeated_continuousActions_codec
        = pb::FieldCodec.ForFloat(50);
    private readonly pbc::RepeatedField<float> continuousActions_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<float> ContinuousActions {
      get { return continuousActions_; }
    }

    /// <summary>Field number for the "discrete_actions" field.</summary>
    public const int DiscreteActionsFieldNumber = 7;
    private static readonly pb::FieldCodec<int> _repeated_discreteActions_codec
        = pb::FieldCodec.ForInt32(58);
    private readonly pbc::RepeatedField<int> discreteActions_ = new pbc::RepeatedField<int>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<int> DiscreteActions {
      get { return discreteActions_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as AgentActionProto);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(AgentActionProto other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!vectorActionsDeprecated_.Equals(other.vectorActionsDeprecated_)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Value, other.Value)) return false;
      if(!continuousActions_.Equals(other.continuousActions_)) return false;
      if(!discreteActions_.Equals(other.discreteActions_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= vectorActionsDeprecated_.GetHashCode();
      if (Value != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Value);
      hash ^= continuousActions_.GetHashCode();
      hash ^= discreteActions_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      vectorActionsDeprecated_.WriteTo(output, _repeated_vectorActionsDeprecated_codec);
      if (Value != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(Value);
      }
      continuousActions_.WriteTo(output, _repeated_continuousActions_codec);
      discreteActions_.WriteTo(output, _repeated_discreteActions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += vectorActionsDeprecated_.CalculateSize(_repeated_vectorActionsDeprecated_codec);
      if (Value != 0F) {
        size += 1 + 4;
      }
      size += continuousActions_.CalculateSize(_repeated_continuousActions_codec);
      size += discreteActions_.CalculateSize(_repeated_discreteActions_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(AgentActionProto other) {
      if (other == null) {
        return;
      }
      vectorActionsDeprecated_.Add(other.vectorActionsDeprecated_);
      if (other.Value != 0F) {
        Value = other.Value;
      }
      continuousActions_.Add(other.continuousActions_);
      discreteActions_.Add(other.discreteActions_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 13: {
            vectorActionsDeprecated_.AddEntriesFrom(input, _repeated_vectorActionsDeprecated_codec);
            break;
          }
          case 37: {
            Value = input.ReadFloat();
            break;
          }
          case 50:
          case 53: {
            continuousActions_.AddEntriesFrom(input, _repeated_continuousActions_codec);
            break;
          }
          case 58:
          case 56: {
            discreteActions_.AddEntriesFrom(input, _repeated_discreteActions_codec);
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
