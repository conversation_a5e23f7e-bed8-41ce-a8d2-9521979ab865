//Important note: For Dense layers NCHW and NHWC as input and output are flattened tensors.
#pragma kernel Dense_L1Cached64
#pragma kernel DenseTiled16x16
#pragma kernel DenseTiled32x32
#pragma kernel DenseTiled64x64

//#pragma kernel Dense_T8x8_R8x8_NHWC  DENSE=1 BLOCK_SIZE=8
#pragma kernel Dense_T8x8_R4x4 DENSE=1 BLOCK_SIZE=4
#pragma kernel Dense_T16x16_R4x4 DENSE=1 BLOCK_SIZE=4

#pragma kernel Dense_Tilled2x2_Cached
//Shader compiler goes OOM when compiling this shader in KERNEL_ASSERTS mode on DX11, thus the FORCE_NO_DEBUG.
#pragma kernel Dense_Tilled4x4_Cached FORCE_NO_DEBUG=1

#pragma kernel MatMulPackB0Bias

#pragma kernel Dense_V_L1Cached64


#include "Tensor.cginc"

TENSOR_DECL(X)
TENSOR_DECL(W)
TENSOR_DECL(B)
TENSOR_DECL(WBK)
TENSOR_DECL_RW(O)
float ffma(float a, float b, float c) { return dot(float2(a, c), float2(b, 1)); } //return a*b+c;} //fastfma(a,b,c); }

#if DENSE
#define FUNC_NAME_CALL(KERNEL, SIZE) KERNEL##SIZE##x##SIZE
#define FUNC_NAME(KERNEL, SIZE) FUNC_NAME_CALL(KERNEL, SIZE)
#define CACHE_NAME_CALL(KERNEL, SIZE, TENSOR) KERNEL##SIZE##x##SIZE##_Cache_##TENSOR
#define CACHE_NAME(KERNEL, SIZE, TENSOR) CACHE_NAME_CALL(KERNEL, SIZE, TENSOR)

//CACHE_DEPTH
//      T                >>X
//16: 178ms     272ms   181ms
// 8: 173ms     395ms   205ms
// 4: 176ms     630ms   260ms
// 2: 205ms     495ms   420ms
// 1: 209ms     980ms    --


//@HARDCODED_DIMS + BUF_OFFSET + lds read index alu opt
//CACHE_DEPTH
//      T                >>X
//16: 169ms     241ms   173ms
// 8: 169ms     356ms   178ms
// 4: 170ms     612ms   209ms
// 2: 178ms     900ms   380ms
// 1: 250ms     875ms    --

//@BLOCKED_W + HARDCODED_DIMS + BUF_OFFSET + lds read index alu opt
//!INCLUDING ValidateData by mistake!
//CACHE_DEPTH
//      T                >>X
//16: 144ms     241ms   155ms
// 8: 158ms     357ms   164ms
// 4: 151ms     630ms   202ms
// 2: 180ms     815ms   350ms
// 1: 258ms     883ms    --
// @TODO: try 32


//============================================
//@BLOCKED_W + BUF_OFFSET + lds read index alu opt
//CACHE_DEPTH
//      T         T      >>X
//   hard_dims
//32: 167ms
//16: 122ms     141ms   140ms
// 8: 136ms     147ms   154ms
// 4: 130ms     141ms   189ms
// 2: 159ms     ***ms   ***ms
// 1: 220ms     ***ms   ***ms
//
//Vega
//32: 172ms
//16: 154ms
// 8: 156ms
// 4: 161ms
// 2: 162ms
// 1: 245ms
//iOS(8layers)
//32: 28ms


//@BLOCKED_W + lds read index alu opt
//16: 134ms     142ms   146ms


//@BLOCKED_W + BUF_OFFSET + optimized read indices
//CACHE_DEPTH
//16: 123ms     131ms   135ms


#define KERNEL_NAME Dense_T16x16_R
#if BLOCK_SIZE == 4
#define TRANSPOSED_X 0
#define SHIFTED_X 1
#define BLOCKED_W 1
#define HARDCODED_DIMS 0
#define BUF_OFFSET 0
#define DOUBLE_BUFFER_LDS_READS 0
#define CACHE_DEPTH 16
groupshared float CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, X)[CACHE_DEPTH*16*BLOCK_SIZE+SHIFTED_X*CACHE_DEPTH];
groupshared float CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, W)[CACHE_DEPTH*16*BLOCK_SIZE];
[numthreads(16,16,1)]
void FUNC_NAME(KERNEL_NAME, BLOCK_SIZE)(uint3 dispatchThreadID : SV_DispatchThreadID, uint3 groupThreadID : SV_GroupThreadID, uint threadIndex : SV_GroupIndex)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    int x = (int)dispatchThreadID.x * BLOCK_SIZE;
    int y = (int)dispatchThreadID.y * BLOCK_SIZE;
    int tx = (int)groupThreadID.x;
    int ty = (int)groupThreadID.y;
    int bx = ((int)dispatchThreadID.x - (int)groupThreadID.x) * BLOCK_SIZE;
    int by = ((int)dispatchThreadID.y - (int)groupThreadID.y) * BLOCK_SIZE;
    int ti = (int)threadIndex;
    int n       = (int)X.GetFlatWidth();
    int strideX = (int)X.GetFlatWidth();
    int strideW = (int)W.GetFlatWidth();
    int strideO = (int)O.GetFlatWidth();
    int offsetX = BUF_OFFSET;
    int offsetW = BUF_OFFSET;
    int offsetO = BUF_OFFSET;
#if HARDCODED_DIMS == 1
    n       = 1024;
    strideX = 1024;
    strideW = 1024;
    strideO = 1024;
#endif

    #define X_ CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, X)
    #define W_ CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, W)

    //if (x >= (int)O.GetFlatWidth()) return;
    //if (y >= (int)O.GetFlatHeight()) return;

    float4 dstA_0, dstA_1, dstA_2, dstA_3;

    dstA_0.x = B.FastGet(x+0);
    dstA_1.x = B.FastGet(x+0);
    dstA_2.x = B.FastGet(x+0);
    dstA_3.x = B.FastGet(x+0);
    dstA_0.y = B.FastGet(x+1);
    dstA_1.y = B.FastGet(x+1);
    dstA_2.y = B.FastGet(x+1);
    dstA_3.y = B.FastGet(x+1);
    dstA_0.z = B.FastGet(x+2);
    dstA_1.z = B.FastGet(x+2);
    dstA_2.z = B.FastGet(x+2);
    dstA_3.z = B.FastGet(x+2);
    dstA_0.w = B.FastGet(x+3);
    dstA_1.w = B.FastGet(x+3);
    dstA_2.w = B.FastGet(x+3);
    dstA_3.w = B.FastGet(x+3);

    int j;
    int readW = strideW * (ti>>6) + bx + (ti&63) + offsetW;
    #if TRANSPOSED_X == 1
    int readX = strideX * (ti>>6) + by + (ti&63) + offsetX;
    #elif SHIFTED_X == 1
    int4 readX = int4(
        strideX * (by + (ti>>4) + 0) + (ti&15) + offsetX,
        strideX * (by + (ti>>4) +16) + (ti&15) + offsetX,
        strideX * (by + (ti>>4) +32) + (ti&15) + offsetX,
        strideX * (by + (ti>>4) +48) + (ti&15) + offsetX);
    #endif

    for (int i = 0; i < n; i += CACHE_DEPTH)
    {

    #if CACHE_DEPTH == 32
        #if BLOCKED_W == 1
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+256] = W.data[strideW * (i + (ti>>6) + 4) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+512] = W.data[strideW * (i + (ti>>6) + 8) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+768] = W.data[strideW * (i + (ti>>6) +12) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+1024]= W.data[strideW * (i + (ti>>6) +16) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+1280]= W.data[strideW * (i + (ti>>6) +20) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+1536]= W.data[strideW * (i + (ti>>6) +24) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+1792]= W.data[strideW * (i + (ti>>6) +28) + bx + (ti&63) + offsetW];
        #else
        #endif

        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>6) + 0) + by + (ti&63) + offsetX];
        X_[ti+256] = X.data[strideX * (i + (ti>>6) + 4) + by + (ti&63) + offsetX];
        X_[ti+512] = X.data[strideX * (i + (ti>>6) + 8) + by + (ti&63) + offsetX];
        X_[ti+768] = X.data[strideX * (i + (ti>>6) +12) + by + (ti&63) + offsetX];
        X_[ti+1024]= X.data[strideX * (i + (ti>>6) +16) + by + (ti&63) + offsetX];
        X_[ti+1280]= X.data[strideX * (i + (ti>>6) +20) + by + (ti&63) + offsetX];
        X_[ti+1536]= X.data[strideX * (i + (ti>>6) +24) + by + (ti&63) + offsetX];
        X_[ti+1792]= X.data[strideX * (i + (ti>>6) +28) + by + (ti&63) + offsetX];
        #elif SHIFTED_X == 1
        // 16x64 => 64x16
        X_[(ti>>5) + 65*(ti&31) + 0] = X.data[strideX * (by + (ti>>5) + 0) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) + 8] = X.data[strideX * (by + (ti>>5) + 8) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) +16] = X.data[strideX * (by + (ti>>5) +16) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) +24] = X.data[strideX * (by + (ti>>5) +24) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) +32] = X.data[strideX * (by + (ti>>5) +32) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) +40] = X.data[strideX * (by + (ti>>5) +40) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) +48] = X.data[strideX * (by + (ti>>5) +48) + i + (ti&31) + offsetX];
        X_[(ti>>5) + 65*(ti&31) +56] = X.data[strideX * (by + (ti>>5) +56) + i + (ti&31) + offsetX];
        #else
        // 16x64 => 64x16
        #endif


    #elif CACHE_DEPTH == 16
        #if BLOCKED_W == 1
        #if HARDCODED_DIMS
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+256] = W.data[strideW * (i + (ti>>6) + 4) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+512] = W.data[strideW * (i + (ti>>6) + 8) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+768] = W.data[strideW * (i + (ti>>6) +12) + bx + (ti&63) + offsetW];
        #else
        [unroll] for (j = 0; j < 4; ++j, readW += strideW * 4)
            W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2) + 256*j] = W.data[readW];
        #endif
        #else
        W_[ti    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        W_[ti+256] = W.data[strideW * (i + (ti>>6) + 4) + bx + (ti&63) + offsetW];
        W_[ti+512] = W.data[strideW * (i + (ti>>6) + 8) + bx + (ti&63) + offsetW];
        W_[ti+768] = W.data[strideW * (i + (ti>>6) +12) + bx + (ti&63) + offsetW];
        #endif

        #if TRANSPOSED_X == 1
        #if HARDCODED_DIMS
        X_[ti    ] = X.data[strideX * (i + (ti>>6) + 0) + by + (ti&63) + offsetX];
        X_[ti+256] = X.data[strideX * (i + (ti>>6) + 4) + by + (ti&63) + offsetX];
        X_[ti+512] = X.data[strideX * (i + (ti>>6) + 8) + by + (ti&63) + offsetX];
        X_[ti+768] = X.data[strideX * (i + (ti>>6) +12) + by + (ti&63) + offsetX];
        #else
        [unroll] for (j = 0; j < 4; ++j, readX += strideX * 4)
            X_[ti + 256*j] = X.data[readX];
        #endif

        #elif SHIFTED_X == 1
        // 16x64 => 64x16
        #if HARDCODED_DIMS
        X_[(ti>>4) + 65*(ti&15) + 0] = X.data[strideX * (by + (ti>>4) + 0) + i + (ti&15) + offsetX];
        X_[(ti>>4) + 65*(ti&15) +16] = X.data[strideX * (by + (ti>>4) +16) + i + (ti&15) + offsetX];
        X_[(ti>>4) + 65*(ti&15) +32] = X.data[strideX * (by + (ti>>4) +32) + i + (ti&15) + offsetX];
        X_[(ti>>4) + 65*(ti&15) +48] = X.data[strideX * (by + (ti>>4) +48) + i + (ti&15) + offsetX];
        #else
        [unroll] for (j = 0; j < 4; ++j)
            X_[(ti>>4) + 65*(ti&15) + 16*j] = X.data[readX[j]];
        readX += CACHE_DEPTH;
        #endif
        #else
        // 16x64 => 64x16
        X_[ti    ] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 0 + offsetX];
        X_[ti+256] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 4 + offsetX];
        X_[ti+512] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 8 + offsetX];
        X_[ti+768] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) +12 + offsetX];
        #endif

    #elif CACHE_DEPTH == 8
        #if BLOCKED_W == 1
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)+256] = W.data[strideW * (i + (ti>>6) + 4) + bx + (ti&63) + offsetW];
        #else
        W_[ti    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        W_[ti+256] = W.data[strideW * (i + (ti>>6) + 4) + bx + (ti&63) + offsetW];
        #endif

        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>6) + 0) + by + (ti&63) + offsetX];
        X_[ti+256] = X.data[strideX * (i + (ti>>6) + 4) + by + (ti&63) + offsetX];
        #elif SHIFTED_X == 1
        // 8x64 => 64x8
        X_[(ti>>3) + 65*(ti&7) + 0] = X.data[strideX * (by + (ti>>3) + 0) + i + (ti&7) + offsetX];
        X_[(ti>>3) + 65*(ti&7) +32] = X.data[strideX * (by + (ti>>3) +32) + i + (ti&7) + offsetX];
        #else
        // 8x64 => 64x8
        X_[ti    ] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 0 + offsetX];
        X_[ti+256] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 4 + offsetX];
        #endif

    #elif CACHE_DEPTH == 4
        #if BLOCKED_W == 1
        W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        #else
        W_[ti    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
        #endif
        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>6) + 0) + by + (ti&63) + offsetX];
        #elif SHIFTED_X == 1
        // 4x64 => 64x4
        X_[(ti>>2) + 65*(ti&3) + 0] = X.data[strideX * (by + (ti>>2) + 0) + i + (ti&3) + offsetX];
        #else
        // 4x64 => 64x4
        X_[ti    ] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 0 + offsetX];
        #endif

    #elif CACHE_DEPTH == 2
        if (ti < 128)
        {
            #if BLOCKED_W == 1
            W_[((ti>>6)<<6) + ((ti&3)<<4) + ((ti&63)>>2)    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
            #else
            W_[ti    ] = W.data[strideW * (i + (ti>>6) + 0) + bx + (ti&63) + offsetW];
            #endif
            #if TRANSPOSED_X == 1
            X_[ti    ] = X.data[strideX * (i + (ti>>6) + 0) + by + (ti&63) + offsetX];
            #elif SHIFTED_X == 1
            X_[(ti>>1) + 65*(ti&1) + 0] = X.data[strideX * (by + (ti>>1) + 0) + i + (ti&1) + offsetX];
            #else
            X_[ti    ] = X.data[strideX * (by + (ti&63)) + i + (ti>>6) + 0 + offsetX];
            #endif
        }

    #elif CACHE_DEPTH == 1
        if (ti < 64)
        {
            #if BLOCKED_W == 1
            W_[((ti&3)<<4) + ((ti&63)>>2)    ] = W.data[strideW * i + bx + ti + offsetW];
            #else
            W_[ti] = W.data[strideW * i + bx + ti + offsetW];
            #endif
            #if TRANSPOSED_X == 1
            X_[ti] = X.data[strideX * i + by + ti + offsetX];
            #else
            //X_[ti] = X.Get(by+ti, i);
            X_[ti] = X.data[strideX * (by + ti) + i + offsetX];
            #endif
        }
    #endif

        GroupMemoryBarrierWithGroupSync();

        int4 idX = int4(0,1,2,3);
        int4 idW = int4(0,1,2,3);
        #if BLOCKED_W == 1
        idW = int4(0,16,32,48);
        #endif
        int incX = 64 + (SHIFTED_X & ~TRANSPOSED_X);
        int incW = 64;
#if 0 //DOUBLE_BUFFER_LDS_READS == 1
        float4 srcW_ = float4(
        #if BLOCKED_W == 1
            W_[idW.x + tx],
            W_[idW.y + tx],
            W_[idW.z + tx],
            W_[idW.w + tx]
        #else
            W_[idW.x + tx*4],
            W_[idW.y + tx*4],
            W_[idW.z + tx*4],
            W_[idW.w + tx*4]
        #endif
        );
        idW += incW;

        //int lastX = idX.x + (CACHE_DEPTH - 2) * incX.x;
        //while (idX.x < lastX.x)
        for (int di = 0; di < CACHE_DEPTH - 2; di+=2)
        {
            float4 srcX, srcW;
            srcX = float4(
                X_[idX.x + ty*4],
                X_[idX.y + ty*4],
                X_[idX.z + ty*4],
                X_[idX.w + ty*4]);
            srcW = float4(
            #if BLOCKED_W == 1
                W_[idW.x + tx],
                W_[idW.y + tx],
                W_[idW.z + tx],
                W_[idW.w + tx]
            #else
                W_[idW.x + tx*4],
                W_[idW.y + tx*4],
                W_[idW.z + tx*4],
                W_[idW.w + tx*4]
            #endif
            );
            idX += incX;
            idW += incW;

            dstA_0.x = ffma(srcX.x, srcW_.x, dstA_0.x);
            dstA_0.y = ffma(srcX.x, srcW_.y, dstA_0.y);
            dstA_0.z = ffma(srcX.x, srcW_.z, dstA_0.z);
            dstA_0.w = ffma(srcX.x, srcW_.w, dstA_0.w);

            dstA_1.x = ffma(srcX.y, srcW_.x, dstA_1.x);
            dstA_1.y = ffma(srcX.y, srcW_.y, dstA_1.y);
            dstA_1.z = ffma(srcX.y, srcW_.z, dstA_1.z);
            dstA_1.w = ffma(srcX.y, srcW_.w, dstA_1.w);

            dstA_2.x = ffma(srcX.z, srcW_.x, dstA_2.x);
            dstA_2.y = ffma(srcX.z, srcW_.y, dstA_2.y);
            dstA_2.z = ffma(srcX.z, srcW_.z, dstA_2.z);
            dstA_2.w = ffma(srcX.z, srcW_.w, dstA_2.w);

            dstA_3.x = ffma(srcX.w, srcW_.x, dstA_3.x);
            dstA_3.y = ffma(srcX.w, srcW_.y, dstA_3.y);
            dstA_3.z = ffma(srcX.w, srcW_.z, dstA_3.z);
            dstA_3.w = ffma(srcX.w, srcW_.w, dstA_3.w);

            srcX = float4(
                X_[idX.x + ty*4],
                X_[idX.y + ty*4],
                X_[idX.z + ty*4],
                X_[idX.w + ty*4]);
            srcW_ = float4(
            #if BLOCKED_W == 1
                W_[idW.x + tx],
                W_[idW.y + tx],
                W_[idW.z + tx],
                W_[idW.w + tx]
            #else
                W_[idW.x + tx*4],
                W_[idW.y + tx*4],
                W_[idW.z + tx*4],
                W_[idW.w + tx*4]
            #endif
            );
            idX += incX;
            idW += incW;

            dstA_0.x = ffma(srcX.x, srcW.x, dstA_0.x);
            dstA_0.y = ffma(srcX.x, srcW.y, dstA_0.y);
            dstA_0.z = ffma(srcX.x, srcW.z, dstA_0.z);
            dstA_0.w = ffma(srcX.x, srcW.w, dstA_0.w);

            dstA_1.x = ffma(srcX.y, srcW.x, dstA_1.x);
            dstA_1.y = ffma(srcX.y, srcW.y, dstA_1.y);
            dstA_1.z = ffma(srcX.y, srcW.z, dstA_1.z);
            dstA_1.w = ffma(srcX.y, srcW.w, dstA_1.w);

            dstA_2.x = ffma(srcX.z, srcW.x, dstA_2.x);
            dstA_2.y = ffma(srcX.z, srcW.y, dstA_2.y);
            dstA_2.z = ffma(srcX.z, srcW.z, dstA_2.z);
            dstA_2.w = ffma(srcX.z, srcW.w, dstA_2.w);

            dstA_3.x = ffma(srcX.w, srcW.x, dstA_3.x);
            dstA_3.y = ffma(srcX.w, srcW.y, dstA_3.y);
            dstA_3.z = ffma(srcX.w, srcW.z, dstA_3.z);
            dstA_3.w = ffma(srcX.w, srcW.w, dstA_3.w);
        }

        float4 srcX = float4(
            X_[idX.x + ty*4],
            X_[idX.y + ty*4],
            X_[idX.z + ty*4],
            X_[idX.w + ty*4]);
        float4 srcW = float4(
        #if BLOCKED_W == 1
            W_[idW.x + tx],
            W_[idW.y + tx],
            W_[idW.z + tx],
            W_[idW.w + tx]
        #else
            W_[idW.x + tx*4],
            W_[idW.y + tx*4],
            W_[idW.z + tx*4],
            W_[idW.w + tx*4]
        #endif
        );

        dstA_0.x = ffma(srcX.x, srcW_.x, dstA_0.x);
        dstA_0.y = ffma(srcX.x, srcW_.y, dstA_0.y);
        dstA_0.z = ffma(srcX.x, srcW_.z, dstA_0.z);
        dstA_0.w = ffma(srcX.x, srcW_.w, dstA_0.w);

        dstA_1.x = ffma(srcX.y, srcW_.x, dstA_1.x);
        dstA_1.y = ffma(srcX.y, srcW_.y, dstA_1.y);
        dstA_1.z = ffma(srcX.y, srcW_.z, dstA_1.z);
        dstA_1.w = ffma(srcX.y, srcW_.w, dstA_1.w);

        dstA_2.x = ffma(srcX.z, srcW_.x, dstA_2.x);
        dstA_2.y = ffma(srcX.z, srcW_.y, dstA_2.y);
        dstA_2.z = ffma(srcX.z, srcW_.z, dstA_2.z);
        dstA_2.w = ffma(srcX.z, srcW_.w, dstA_2.w);

        dstA_3.x = ffma(srcX.w, srcW_.x, dstA_3.x);
        dstA_3.y = ffma(srcX.w, srcW_.y, dstA_3.y);
        dstA_3.z = ffma(srcX.w, srcW_.z, dstA_3.z);
        dstA_3.w = ffma(srcX.w, srcW_.w, dstA_3.w);

        srcX = float4(
            X_[idX.x + ty*4],
            X_[idX.y + ty*4],
            X_[idX.z + ty*4],
            X_[idX.w + ty*4]);
        idX += incX;

        dstA_0.x = ffma(srcX.x, srcW.x, dstA_0.x);
        dstA_0.y = ffma(srcX.x, srcW.y, dstA_0.y);
        dstA_0.z = ffma(srcX.x, srcW.z, dstA_0.z);
        dstA_0.w = ffma(srcX.x, srcW.w, dstA_0.w);

        dstA_1.x = ffma(srcX.y, srcW.x, dstA_1.x);
        dstA_1.y = ffma(srcX.y, srcW.y, dstA_1.y);
        dstA_1.z = ffma(srcX.y, srcW.z, dstA_1.z);
        dstA_1.w = ffma(srcX.y, srcW.w, dstA_1.w);

        dstA_2.x = ffma(srcX.z, srcW.x, dstA_2.x);
        dstA_2.y = ffma(srcX.z, srcW.y, dstA_2.y);
        dstA_2.z = ffma(srcX.z, srcW.z, dstA_2.z);
        dstA_2.w = ffma(srcX.z, srcW.w, dstA_2.w);

        dstA_3.x = ffma(srcX.w, srcW.x, dstA_3.x);
        dstA_3.y = ffma(srcX.w, srcW.y, dstA_3.y);
        dstA_3.z = ffma(srcX.w, srcW.z, dstA_3.z);
        dstA_3.w = ffma(srcX.w, srcW.w, dstA_3.w);


        GroupMemoryBarrierWithGroupSync();
    }
#else // DOUBLE_BUFFER_LDS_READS

#define CACHE_UNROLL 1
        for (int di = 0; di < CACHE_DEPTH; di+=CACHE_UNROLL)
        {
            float4 srcX = float4(
                X_[idX.x + /*ti+0**/ ty*4],
                X_[idX.y + /*ti+0**/ ty*4],
                X_[idX.z + /*ti+0**/ ty*4],
                X_[idX.w + /*ti+0**/ ty*4]);
                //X_[di*_64 + ty*4 + 0],
                //X_[di*_64 + ty*4 + 1],
                //X_[di*_64 + ty*4 + 2],
                //X_[di*_64 + ty*4 + 3]);
                //X.Get(y+0, i+di),
                //X.Get(y+1, i+di),
                //X.Get(y+2, i+di),
                //X.Get(y+3, i+di));
            float4 srcW = float4(
            #if BLOCKED_W == 1
                W_[idW.x + tx],
                W_[idW.y + tx],
                W_[idW.z + tx],
                W_[idW.w + tx]
            #else
                W_[idW.x + tx*4],
                W_[idW.y + tx*4],
                W_[idW.z + tx*4],
                W_[idW.w + tx*4]
            #endif
                //W_[di*64 + tx*4 + 0],
                //W_[di*64 + tx*4 + 1],
                //W_[di*64 + tx*4 + 2],
                //W_[di*64 + tx*4 + 3]
                //W.Get(i+di, x+0),
                //W.Get(i+di, x+1),
                //W.Get(i+di, x+2),
                //W.Get(i+di, x+3)
            );
            idX += incX;
            idW += incW;

            dstA_0.x = ffma(srcX.x, srcW.x, dstA_0.x);
            dstA_0.y = ffma(srcX.x, srcW.y, dstA_0.y);
            dstA_0.z = ffma(srcX.x, srcW.z, dstA_0.z);
            dstA_0.w = ffma(srcX.x, srcW.w, dstA_0.w);

            dstA_1.x = ffma(srcX.y, srcW.x, dstA_1.x);
            dstA_1.y = ffma(srcX.y, srcW.y, dstA_1.y);
            dstA_1.z = ffma(srcX.y, srcW.z, dstA_1.z);
            dstA_1.w = ffma(srcX.y, srcW.w, dstA_1.w);

            dstA_2.x = ffma(srcX.z, srcW.x, dstA_2.x);
            dstA_2.y = ffma(srcX.z, srcW.y, dstA_2.y);
            dstA_2.z = ffma(srcX.z, srcW.z, dstA_2.z);
            dstA_2.w = ffma(srcX.z, srcW.w, dstA_2.w);

            dstA_3.x = ffma(srcX.w, srcW.x, dstA_3.x);
            dstA_3.y = ffma(srcX.w, srcW.y, dstA_3.y);
            dstA_3.z = ffma(srcX.w, srcW.z, dstA_3.z);
            dstA_3.w = ffma(srcX.w, srcW.w, dstA_3.w);

#if CACHE_UNROLL>=2
#endif
#if CACHE_UNROLL>=3
#endif
#if CACHE_UNROLL>=4
#endif
        }

        GroupMemoryBarrierWithGroupSync();
    }
#undef CACHE_UNROLL
#endif //DOUBLE_BUFFER_LDS_READS

    O.FastSetWithActivation(strideO * (y+0) + x+0 + offsetO, dstA_0.x);
    O.FastSetWithActivation(strideO * (y+0) + x+1 + offsetO, dstA_0.y);
    O.FastSetWithActivation(strideO * (y+0) + x+2 + offsetO, dstA_0.z);
    O.FastSetWithActivation(strideO * (y+0) + x+3 + offsetO, dstA_0.w);
    O.FastSetWithActivation(strideO * (y+1) + x+0 + offsetO, dstA_1.x);
    O.FastSetWithActivation(strideO * (y+1) + x+1 + offsetO, dstA_1.y);
    O.FastSetWithActivation(strideO * (y+1) + x+2 + offsetO, dstA_1.z);
    O.FastSetWithActivation(strideO * (y+1) + x+3 + offsetO, dstA_1.w);
    O.FastSetWithActivation(strideO * (y+2) + x+0 + offsetO, dstA_2.x);
    O.FastSetWithActivation(strideO * (y+2) + x+1 + offsetO, dstA_2.y);
    O.FastSetWithActivation(strideO * (y+2) + x+2 + offsetO, dstA_2.z);
    O.FastSetWithActivation(strideO * (y+2) + x+3 + offsetO, dstA_2.w);
    O.FastSetWithActivation(strideO * (y+3) + x+0 + offsetO, dstA_3.x);
    O.FastSetWithActivation(strideO * (y+3) + x+1 + offsetO, dstA_3.y);
    O.FastSetWithActivation(strideO * (y+3) + x+2 + offsetO, dstA_3.z);
    O.FastSetWithActivation(strideO * (y+3) + x+3 + offsetO, dstA_3.w);

    #undef X_
    #undef W_
}
#undef TRANSPOSED_X
#undef SHIFTED_X
#undef BLOCKED_W
#undef HARDCODED_DIMS
#undef BUF_OFFSET
#undef DOUBLE_BUFFER_LDS_READS
#undef CACHE_DEPTH
#else
[numthreads(16,16,1)]
void FUNC_NAME(KERNEL_NAME, BLOCK_SIZE)(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    int x = (int)dispatchThreadID.x * BLOCK_SIZE;
    int y = (int)dispatchThreadID.y * BLOCK_SIZE;
    int n = (int)X.GetFlatWidth();

    if (x >= (int)O.GetFlatWidth()) return;
    if (y >= (int)O.GetFlatHeight()) return;

    float dstA[BLOCK_SIZE][BLOCK_SIZE];
    float srcX[BLOCK_SIZE];

    int dy, dx;
    for (dx = 0; dx < BLOCK_SIZE; ++dx)
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            dstA[dy][dx] = B.data[x+dx+B.offset];//B.Get(x+dx);

    for (int i = 0; i < n; ++i)
    {
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            srcX[dy] = X.data[(y+dy)*X.channels+i];//X.Get(y+dy, i);

        for (dx = 0; dx < BLOCK_SIZE; ++dx)
        {
            float srcW = W.data[i*W.channels+x+dx];//W.Get(i, x+dx);
            for (dy = 0; dy < BLOCK_SIZE; ++dy)
                dstA[dy][dx] += srcX[dy] * srcW;
        }
    }

    for (dx = 0; dx < BLOCK_SIZE; ++dx)
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            O.SetWithActivation(y+dy, x+dx, dstA[dy][dx]);
}
#endif
#undef KERNEL_NAME


//CACHE_DEPTH
//      T        >>X
//16: 183ms     207ms
// 8: 158ms     202ms
// 4: 162ms     334ms
// 2: 159ms     ***ms
// 1: 173ms      --

#define KERNEL_NAME Dense_T8x8_R
#if BLOCK_SIZE == 8
#define UNROLL_INNER_LOOP 0
#define TRANSPOSED_X 0
#define HARDCODED_DIMS 0
#define BUF_OFFSET 0
#define CACHE_DEPTH 8
groupshared float CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, X)[CACHE_DEPTH*8*BLOCK_SIZE+(1-TRANSPOSED_X)*CACHE_DEPTH];
groupshared float CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, W)[CACHE_DEPTH*8*BLOCK_SIZE];
[numthreads(8,8,1)]
void FUNC_NAME(KERNEL_NAME, BLOCK_SIZE)(uint3 dispatchThreadID : SV_DispatchThreadID, uint3 groupThreadID : SV_GroupThreadID, uint threadIndex : SV_GroupIndex)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    int x = (int)dispatchThreadID.x * BLOCK_SIZE;
    int y = (int)dispatchThreadID.y * BLOCK_SIZE;
    int tx = (int)groupThreadID.x;
    int ty = (int)groupThreadID.y;
    int bx = ((int)dispatchThreadID.x - (int)groupThreadID.x) * BLOCK_SIZE;
    int by = ((int)dispatchThreadID.y - (int)groupThreadID.y) * BLOCK_SIZE;
    int ti = (int)threadIndex;
    int n       = (int)X.GetFlatWidth();
    int strideX = (int)X.GetFlatWidth();
    int strideW = (int)W.GetFlatWidth();
    int strideO = (int)O.GetFlatWidth();
    int offsetX = BUF_OFFSET;
    int offsetW = BUF_OFFSET;
    int offsetO = BUF_OFFSET;
#if HARDCODED_DIMS == 1
    n       = 1024;
    strideX = 1024;
    strideW = 1024;
    strideO = 1024;
#endif

    #define X_ CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, X)
    #define W_ CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, W)

#if UNROLL_INNER_LOOP
    float4 dstA_0, dstA_1, dstA_2, dstA_3;
    float4 dstB_0, dstB_1, dstB_2, dstB_3;
    float4 dstC_0, dstC_1, dstC_2, dstC_3;
    float4 dstD_0, dstD_1, dstD_2, dstD_3;

    dstA_0.x = dstC_0.x = B.FastGet(x+0);
    dstA_1.x = dstC_1.x = B.FastGet(x+0);
    dstA_2.x = dstC_2.x = B.FastGet(x+0);
    dstA_3.x = dstC_3.x = B.FastGet(x+0);
    dstA_0.y = dstC_0.y = B.FastGet(x+1);
    dstA_1.y = dstC_1.y = B.FastGet(x+1);
    dstA_2.y = dstC_2.y = B.FastGet(x+1);
    dstA_3.y = dstC_3.y = B.FastGet(x+1);
    dstA_0.z = dstC_0.z = B.FastGet(x+2);
    dstA_1.z = dstC_1.z = B.FastGet(x+2);
    dstA_2.z = dstC_2.z = B.FastGet(x+2);
    dstA_3.z = dstC_3.z = B.FastGet(x+2);
    dstA_0.w = dstC_0.w = B.FastGet(x+3);
    dstA_1.w = dstC_1.w = B.FastGet(x+3);
    dstA_2.w = dstC_2.w = B.FastGet(x+3);
    dstA_3.w = dstC_3.w = B.FastGet(x+3);

    dstB_0.x = dstD_0.x = B.FastGet(x+4);
    dstB_1.x = dstD_1.x = B.FastGet(x+4);
    dstB_2.x = dstD_2.x = B.FastGet(x+4);
    dstB_3.x = dstD_3.x = B.FastGet(x+4);
    dstB_0.y = dstD_0.y = B.FastGet(x+5);
    dstB_1.y = dstD_1.y = B.FastGet(x+5);
    dstB_2.y = dstD_2.y = B.FastGet(x+5);
    dstB_3.y = dstD_3.y = B.FastGet(x+5);
    dstB_0.z = dstD_0.z = B.FastGet(x+6);
    dstB_1.z = dstD_1.z = B.FastGet(x+6);
    dstB_2.z = dstD_2.z = B.FastGet(x+6);
    dstB_3.z = dstD_3.z = B.FastGet(x+6);
    dstB_0.w = dstD_0.w = B.FastGet(x+7);
    dstB_1.w = dstD_1.w = B.FastGet(x+7);
    dstB_2.w = dstD_2.w = B.FastGet(x+7);
    dstB_3.w = dstD_3.w = B.FastGet(x+7);
#else
    float4 dstA_0[4], dstA_1[4], dstA_2[4], dstA_3[4];
    dstA_0[0].x = dstA_0[2].x = B.FastGet(x+0);
    dstA_1[0].x = dstA_1[2].x = B.FastGet(x+0);
    dstA_2[0].x = dstA_2[2].x = B.FastGet(x+0);
    dstA_3[0].x = dstA_3[2].x = B.FastGet(x+0);
    dstA_0[0].y = dstA_0[2].y = B.FastGet(x+1);
    dstA_1[0].y = dstA_1[2].y = B.FastGet(x+1);
    dstA_2[0].y = dstA_2[2].y = B.FastGet(x+1);
    dstA_3[0].y = dstA_3[2].y = B.FastGet(x+1);
    dstA_0[0].z = dstA_0[2].z = B.FastGet(x+2);
    dstA_1[0].z = dstA_1[2].z = B.FastGet(x+2);
    dstA_2[0].z = dstA_2[2].z = B.FastGet(x+2);
    dstA_3[0].z = dstA_3[2].z = B.FastGet(x+2);
    dstA_0[0].w = dstA_0[2].w = B.FastGet(x+3);
    dstA_1[0].w = dstA_1[2].w = B.FastGet(x+3);
    dstA_2[0].w = dstA_2[2].w = B.FastGet(x+3);
    dstA_3[0].w = dstA_3[2].w = B.FastGet(x+3);

    dstA_0[1].x = dstA_0[3].x = B.FastGet(x+4);
    dstA_1[1].x = dstA_1[3].x = B.FastGet(x+4);
    dstA_2[1].x = dstA_2[3].x = B.FastGet(x+4);
    dstA_3[1].x = dstA_3[3].x = B.FastGet(x+4);
    dstA_0[1].y = dstA_0[3].y = B.FastGet(x+5);
    dstA_1[1].y = dstA_1[3].y = B.FastGet(x+5);
    dstA_2[1].y = dstA_2[3].y = B.FastGet(x+5);
    dstA_3[1].y = dstA_3[3].y = B.FastGet(x+5);
    dstA_0[1].z = dstA_0[3].z = B.FastGet(x+6);
    dstA_1[1].z = dstA_1[3].z = B.FastGet(x+6);
    dstA_2[1].z = dstA_2[3].z = B.FastGet(x+6);
    dstA_3[1].z = dstA_3[3].z = B.FastGet(x+6);
    dstA_0[1].w = dstA_0[3].w = B.FastGet(x+7);
    dstA_1[1].w = dstA_1[3].w = B.FastGet(x+7);
    dstA_2[1].w = dstA_2[3].w = B.FastGet(x+7);
    dstA_3[1].w = dstA_3[3].w = B.FastGet(x+7);

#endif

    for (int i = 0; i < n; i += CACHE_DEPTH)
    {
    #if TRANSPOSED_X == 1
        [unroll]
        for (int j = 0; j < CACHE_DEPTH; ++j)
        {
            X_[ti + j*64] = X.data[strideX * (i + j) + by + ti + offsetX];

            // split 64 into 8 blocks and interleave them
            // 000000001111111122222222... => 012345678012345678...
            W_[((ti&7)<<3) + (ti>>3) + j*64] = W.data[strideW * (i + j) + bx + ti + offsetW];
        }
    #else
        int tiDiv = (uint)ti/CACHE_DEPTH;
        int tiMod = ti&(CACHE_DEPTH-1);
        int jStride = 64/CACHE_DEPTH;

        [unroll]
        for (int j = 0; j < CACHE_DEPTH; ++j)
        {
            // CACHE_DEPTHx64 => 64xCACHE_DEPTH
            X_[tiDiv + 65*tiMod + j*jStride] = X.data[strideX * (by + tiDiv + j*jStride) + i + tiMod];

            // split 64 into 8 blocks and interleave them
            // 000000001111111122222222... => 012345678012345678...
            W_[((ti&7)<<3) + (ti>>3) + j*64] = W.data[strideW * (i + j) + bx + ti + offsetW];
        }
    #endif

        GroupMemoryBarrierWithGroupSync();

#if UNROLL_INNER_LOOP
        int4 idX0 = int4(0,1,2,3);      int4 idX1 = int4(4,5,6,7);
        int4 idW0 = int4(0,8,16,24);    int4 idW1 = int4(32,40,48,56);
#else
        int4 idX[2], idW[2];
        idX[0] = int4(0,1,2,3);     idX[1] = int4(4,5,6,7);
        idW[0] = int4(0,8,16,24);   idW[1] = int4(32,40,48,56);
#endif
        int incX = 64 + (TRANSPOSED_X?0:1);
        int incW = 64;
        for (int di = 0; di < CACHE_DEPTH; di++)
        {
#if UNROLL_INNER_LOOP
            float4 srcX0 = float4(
                X_[idX0.x + ty*8],
                X_[idX0.y + ty*8],
                X_[idX0.z + ty*8],
                X_[idX0.w + ty*8]);
            float4 srcX1 = float4(
                X_[idX1.x + ty*8],
                X_[idX1.y + ty*8],
                X_[idX1.z + ty*8],
                X_[idX1.w + ty*8]);
            float4 srcW0 = float4(
                W_[idW0.x + tx],
                W_[idW0.y + tx],
                W_[idW0.z + tx],
                W_[idW0.w + tx]);
            float4 srcW1 = float4(
                W_[idW1.x + tx],
                W_[idW1.y + tx],
                W_[idW1.z + tx],
                W_[idW1.w + tx]);
            idX0 += incX; idX1 += incX;
            idW0 += incW; idW1 += incW;

            dstA_0.x = ffma(srcX0.x, srcW0.x, dstA_0.x);
            dstA_0.y = ffma(srcX0.x, srcW0.y, dstA_0.y);
            dstA_0.z = ffma(srcX0.x, srcW0.z, dstA_0.z);
            dstA_0.w = ffma(srcX0.x, srcW0.w, dstA_0.w);
            dstA_1.x = ffma(srcX0.y, srcW0.x, dstA_1.x);
            dstA_1.y = ffma(srcX0.y, srcW0.y, dstA_1.y);
            dstA_1.z = ffma(srcX0.y, srcW0.z, dstA_1.z);
            dstA_1.w = ffma(srcX0.y, srcW0.w, dstA_1.w);
            dstA_2.x = ffma(srcX0.z, srcW0.x, dstA_2.x);
            dstA_2.y = ffma(srcX0.z, srcW0.y, dstA_2.y);
            dstA_2.z = ffma(srcX0.z, srcW0.z, dstA_2.z);
            dstA_2.w = ffma(srcX0.z, srcW0.w, dstA_2.w);
            dstA_3.x = ffma(srcX0.w, srcW0.x, dstA_3.x);
            dstA_3.y = ffma(srcX0.w, srcW0.y, dstA_3.y);
            dstA_3.z = ffma(srcX0.w, srcW0.z, dstA_3.z);
            dstA_3.w = ffma(srcX0.w, srcW0.w, dstA_3.w);

            //
            dstB_0.x = ffma(srcX0.x, srcW1.x, dstB_0.x);
            dstB_0.y = ffma(srcX0.x, srcW1.y, dstB_0.y);
            dstB_0.z = ffma(srcX0.x, srcW1.z, dstB_0.z);
            dstB_0.w = ffma(srcX0.x, srcW1.w, dstB_0.w);
            dstB_1.x = ffma(srcX0.y, srcW1.x, dstB_1.x);
            dstB_1.y = ffma(srcX0.y, srcW1.y, dstB_1.y);
            dstB_1.z = ffma(srcX0.y, srcW1.z, dstB_1.z);
            dstB_1.w = ffma(srcX0.y, srcW1.w, dstB_1.w);
            dstB_2.x = ffma(srcX0.z, srcW1.x, dstB_2.x);
            dstB_2.y = ffma(srcX0.z, srcW1.y, dstB_2.y);
            dstB_2.z = ffma(srcX0.z, srcW1.z, dstB_2.z);
            dstB_2.w = ffma(srcX0.z, srcW1.w, dstB_2.w);
            dstB_3.x = ffma(srcX0.w, srcW1.x, dstB_3.x);
            dstB_3.y = ffma(srcX0.w, srcW1.y, dstB_3.y);
            dstB_3.z = ffma(srcX0.w, srcW1.z, dstB_3.z);
            dstB_3.w = ffma(srcX0.w, srcW1.w, dstB_3.w);

            //
            dstC_0.x = ffma(srcX1.x, srcW0.x, dstC_0.x);
            dstC_0.y = ffma(srcX1.x, srcW0.y, dstC_0.y);
            dstC_0.z = ffma(srcX1.x, srcW0.z, dstC_0.z);
            dstC_0.w = ffma(srcX1.x, srcW0.w, dstC_0.w);
            dstC_1.x = ffma(srcX1.y, srcW0.x, dstC_1.x);
            dstC_1.y = ffma(srcX1.y, srcW0.y, dstC_1.y);
            dstC_1.z = ffma(srcX1.y, srcW0.z, dstC_1.z);
            dstC_1.w = ffma(srcX1.y, srcW0.w, dstC_1.w);
            dstC_2.x = ffma(srcX1.z, srcW0.x, dstC_2.x);
            dstC_2.y = ffma(srcX1.z, srcW0.y, dstC_2.y);
            dstC_2.z = ffma(srcX1.z, srcW0.z, dstC_2.z);
            dstC_2.w = ffma(srcX1.z, srcW0.w, dstC_2.w);
            dstC_3.x = ffma(srcX1.w, srcW0.x, dstC_3.x);
            dstC_3.y = ffma(srcX1.w, srcW0.y, dstC_3.y);
            dstC_3.z = ffma(srcX1.w, srcW0.z, dstC_3.z);
            dstC_3.w = ffma(srcX1.w, srcW0.w, dstC_3.w);

            //
            dstD_0.x = ffma(srcX1.x, srcW1.x, dstD_0.x);
            dstD_0.y = ffma(srcX1.x, srcW1.y, dstD_0.y);
            dstD_0.z = ffma(srcX1.x, srcW1.z, dstD_0.z);
            dstD_0.w = ffma(srcX1.x, srcW1.w, dstD_0.w);
            dstD_1.x = ffma(srcX1.y, srcW1.x, dstD_1.x);
            dstD_1.y = ffma(srcX1.y, srcW1.y, dstD_1.y);
            dstD_1.z = ffma(srcX1.y, srcW1.z, dstD_1.z);
            dstD_1.w = ffma(srcX1.y, srcW1.w, dstD_1.w);
            dstD_2.x = ffma(srcX1.z, srcW1.x, dstD_2.x);
            dstD_2.y = ffma(srcX1.z, srcW1.y, dstD_2.y);
            dstD_2.z = ffma(srcX1.z, srcW1.z, dstD_2.z);
            dstD_2.w = ffma(srcX1.z, srcW1.w, dstD_2.w);
            dstD_3.x = ffma(srcX1.w, srcW1.x, dstD_3.x);
            dstD_3.y = ffma(srcX1.w, srcW1.y, dstD_3.y);
            dstD_3.z = ffma(srcX1.w, srcW1.z, dstD_3.z);
            dstD_3.w = ffma(srcX1.w, srcW1.w, dstD_3.w);

#else
            float4 srcX[2], srcW[2];
            srcX[0] = float4(
                X_[idX[0].x + ty*8],
                X_[idX[0].y + ty*8],
                X_[idX[0].z + ty*8],
                X_[idX[0].w + ty*8]);
            srcX[1] = float4(
                X_[idX[1].x + ty*8],
                X_[idX[1].y + ty*8],
                X_[idX[1].z + ty*8],
                X_[idX[1].w + ty*8]);
            srcW[0] = float4(
                W_[idW[0].x + tx],
                W_[idW[0].y + tx],
                W_[idW[0].z + tx],
                W_[idW[0].w + tx]);
            srcW[1] = float4(
                W_[idW[1].x + tx],
                W_[idW[1].y + tx],
                W_[idW[1].z + tx],
                W_[idW[1].w + tx]);
            idX[0] += incX; idX[1] += incX;
            idW[0] += incW; idW[1] += incW;


            [loop]
            for (uint qw = 0; qw < 4; ++qw)
            {
                uint q = qw >> 1;
                uint w = qw  & 1;
                dstA_0[qw].x = ffma(srcX[q].x, srcW[w].x, dstA_0[qw].x);
                dstA_0[qw].y = ffma(srcX[q].x, srcW[w].y, dstA_0[qw].y);
                dstA_0[qw].z = ffma(srcX[q].x, srcW[w].z, dstA_0[qw].z);
                dstA_0[qw].w = ffma(srcX[q].x, srcW[w].w, dstA_0[qw].w);
                dstA_1[qw].x = ffma(srcX[q].y, srcW[w].x, dstA_1[qw].x);
                dstA_1[qw].y = ffma(srcX[q].y, srcW[w].y, dstA_1[qw].y);
                dstA_1[qw].z = ffma(srcX[q].y, srcW[w].z, dstA_1[qw].z);
                dstA_1[qw].w = ffma(srcX[q].y, srcW[w].w, dstA_1[qw].w);
                dstA_2[qw].x = ffma(srcX[q].z, srcW[w].x, dstA_2[qw].x);
                dstA_2[qw].y = ffma(srcX[q].z, srcW[w].y, dstA_2[qw].y);
                dstA_2[qw].z = ffma(srcX[q].z, srcW[w].z, dstA_2[qw].z);
                dstA_2[qw].w = ffma(srcX[q].z, srcW[w].w, dstA_2[qw].w);
                dstA_3[qw].x = ffma(srcX[q].w, srcW[w].x, dstA_3[qw].x);
                dstA_3[qw].y = ffma(srcX[q].w, srcW[w].y, dstA_3[qw].y);
                dstA_3[qw].z = ffma(srcX[q].w, srcW[w].z, dstA_3[qw].z);
                dstA_3[qw].w = ffma(srcX[q].w, srcW[w].w, dstA_3[qw].w);
            }
#endif
        }

        GroupMemoryBarrierWithGroupSync();
    }
#if UNROLL_INNER_LOOP
    O.FastSetWithActivation(strideO * (y+0) + x+0 + offsetO], dstA_0.x);
    O.FastSetWithActivation(strideO * (y+0) + x+1 + offsetO], dstA_0.y);
    O.FastSetWithActivation(strideO * (y+0) + x+2 + offsetO], dstA_0.z);
    O.FastSetWithActivation(strideO * (y+0) + x+3 + offsetO], dstA_0.w);
    O.FastSetWithActivation(strideO * (y+0) + x+4 + offsetO], dstB_0.x);
    O.FastSetWithActivation(strideO * (y+0) + x+5 + offsetO], dstB_0.y);
    O.FastSetWithActivation(strideO * (y+0) + x+6 + offsetO], dstB_0.z);
    O.FastSetWithActivation(strideO * (y+0) + x+7 + offsetO], dstB_0.w);
    O.FastSetWithActivation(strideO * (y+1) + x+0 + offsetO], dstA_1.x);
    O.FastSetWithActivation(strideO * (y+1) + x+1 + offsetO], dstA_1.y);
    O.FastSetWithActivation(strideO * (y+1) + x+2 + offsetO], dstA_1.z);
    O.FastSetWithActivation(strideO * (y+1) + x+3 + offsetO], dstA_1.w);
    O.FastSetWithActivation(strideO * (y+1) + x+4 + offsetO], dstB_1.x);
    O.FastSetWithActivation(strideO * (y+1) + x+5 + offsetO], dstB_1.y);
    O.FastSetWithActivation(strideO * (y+1) + x+6 + offsetO], dstB_1.z);
    O.FastSetWithActivation(strideO * (y+1) + x+7 + offsetO], dstB_1.w);
    O.FastSetWithActivation(strideO * (y+2) + x+0 + offsetO], dstA_2.x);
    O.FastSetWithActivation(strideO * (y+2) + x+1 + offsetO], dstA_2.y);
    O.FastSetWithActivation(strideO * (y+2) + x+2 + offsetO], dstA_2.z);
    O.FastSetWithActivation(strideO * (y+2) + x+3 + offsetO], dstA_2.w);
    O.FastSetWithActivation(strideO * (y+2) + x+4 + offsetO], dstB_2.x);
    O.FastSetWithActivation(strideO * (y+2) + x+5 + offsetO], dstB_2.y);
    O.FastSetWithActivation(strideO * (y+2) + x+6 + offsetO], dstB_2.z);
    O.FastSetWithActivation(strideO * (y+2) + x+7 + offsetO], dstB_2.w);
    O.FastSetWithActivation(strideO * (y+3) + x+0 + offsetO], dstA_3.x);
    O.FastSetWithActivation(strideO * (y+3) + x+1 + offsetO], dstA_3.y);
    O.FastSetWithActivation(strideO * (y+3) + x+2 + offsetO], dstA_3.z);
    O.FastSetWithActivation(strideO * (y+3) + x+3 + offsetO], dstA_3.w);
    O.FastSetWithActivation(strideO * (y+3) + x+4 + offsetO], dstB_3.x);
    O.FastSetWithActivation(strideO * (y+3) + x+5 + offsetO], dstB_3.y);
    O.FastSetWithActivation(strideO * (y+3) + x+6 + offsetO], dstB_3.z);
    O.FastSetWithActivation(strideO * (y+3) + x+7 + offsetO], dstB_3.w);

    O.FastSetWithActivation(strideO * (y+4) + x+0 + offsetO], dstC_0.x);
    O.FastSetWithActivation(strideO * (y+4) + x+1 + offsetO], dstC_0.y);
    O.FastSetWithActivation(strideO * (y+4) + x+2 + offsetO], dstC_0.z);
    O.FastSetWithActivation(strideO * (y+4) + x+3 + offsetO], dstC_0.w);
    O.FastSetWithActivation(strideO * (y+4) + x+4 + offsetO], dstD_0.x);
    O.FastSetWithActivation(strideO * (y+4) + x+5 + offsetO], dstD_0.y);
    O.FastSetWithActivation(strideO * (y+4) + x+6 + offsetO], dstD_0.z);
    O.FastSetWithActivation(strideO * (y+4) + x+7 + offsetO], dstD_0.w);
    O.FastSetWithActivation(strideO * (y+5) + x+0 + offsetO], dstC_1.x);
    O.FastSetWithActivation(strideO * (y+5) + x+1 + offsetO], dstC_1.y);
    O.FastSetWithActivation(strideO * (y+5) + x+2 + offsetO], dstC_1.z);
    O.FastSetWithActivation(strideO * (y+5) + x+3 + offsetO], dstC_1.w);
    O.FastSetWithActivation(strideO * (y+5) + x+4 + offsetO], dstD_1.x);
    O.FastSetWithActivation(strideO * (y+5) + x+5 + offsetO], dstD_1.y);
    O.FastSetWithActivation(strideO * (y+5) + x+6 + offsetO], dstD_1.z);
    O.FastSetWithActivation(strideO * (y+5) + x+7 + offsetO], dstD_1.w);
    O.FastSetWithActivation(strideO * (y+6) + x+0 + offsetO], dstC_2.x);
    O.FastSetWithActivation(strideO * (y+6) + x+1 + offsetO], dstC_2.y);
    O.FastSetWithActivation(strideO * (y+6) + x+2 + offsetO], dstC_2.z);
    O.FastSetWithActivation(strideO * (y+6) + x+3 + offsetO], dstC_2.w);
    O.FastSetWithActivation(strideO * (y+6) + x+4 + offsetO], dstD_2.x);
    O.FastSetWithActivation(strideO * (y+6) + x+5 + offsetO], dstD_2.y);
    O.FastSetWithActivation(strideO * (y+6) + x+6 + offsetO], dstD_2.z);
    O.FastSetWithActivation(strideO * (y+6) + x+7 + offsetO], dstD_2.w);
    O.FastSetWithActivation(strideO * (y+7) + x+0 + offsetO], dstC_3.x);
    O.FastSetWithActivation(strideO * (y+7) + x+1 + offsetO], dstC_3.y);
    O.FastSetWithActivation(strideO * (y+7) + x+2 + offsetO], dstC_3.z);
    O.FastSetWithActivation(strideO * (y+7) + x+3 + offsetO], dstC_3.w);
    O.FastSetWithActivation(strideO * (y+7) + x+4 + offsetO], dstD_3.x);
    O.FastSetWithActivation(strideO * (y+7) + x+5 + offsetO], dstD_3.y);
    O.FastSetWithActivation(strideO * (y+7) + x+6 + offsetO], dstD_3.z);
    O.FastSetWithActivation(strideO * (y+7) + x+7 + offsetO], dstD_3.w);
#else
    O.FastSetWithActivation(strideO * (y+0) + x+0 + offsetO], dstA_0[0].x);
    O.FastSetWithActivation(strideO * (y+0) + x+1 + offsetO], dstA_0[0].y);
    O.FastSetWithActivation(strideO * (y+0) + x+2 + offsetO], dstA_0[0].z);
    O.FastSetWithActivation(strideO * (y+0) + x+3 + offsetO], dstA_0[0].w);
    O.FastSetWithActivation(strideO * (y+0) + x+4 + offsetO], dstA_0[1].x);
    O.FastSetWithActivation(strideO * (y+0) + x+5 + offsetO], dstA_0[1].y);
    O.FastSetWithActivation(strideO * (y+0) + x+6 + offsetO], dstA_0[1].z);
    O.FastSetWithActivation(strideO * (y+0) + x+7 + offsetO], dstA_0[1].w);
    O.FastSetWithActivation(strideO * (y+1) + x+0 + offsetO], dstA_1[0].x);
    O.FastSetWithActivation(strideO * (y+1) + x+1 + offsetO], dstA_1[0].y);
    O.FastSetWithActivation(strideO * (y+1) + x+2 + offsetO], dstA_1[0].z);
    O.FastSetWithActivation(strideO * (y+1) + x+3 + offsetO], dstA_1[0].w);
    O.FastSetWithActivation(strideO * (y+1) + x+4 + offsetO], dstA_1[1].x);
    O.FastSetWithActivation(strideO * (y+1) + x+5 + offsetO], dstA_1[1].y);
    O.FastSetWithActivation(strideO * (y+1) + x+6 + offsetO], dstA_1[1].z);
    O.FastSetWithActivation(strideO * (y+1) + x+7 + offsetO], dstA_1[1].w);
    O.FastSetWithActivation(strideO * (y+2) + x+0 + offsetO], dstA_2[0].x);
    O.FastSetWithActivation(strideO * (y+2) + x+1 + offsetO], dstA_2[0].y);
    O.FastSetWithActivation(strideO * (y+2) + x+2 + offsetO], dstA_2[0].z);
    O.FastSetWithActivation(strideO * (y+2) + x+3 + offsetO], dstA_2[0].w);
    O.FastSetWithActivation(strideO * (y+2) + x+4 + offsetO], dstA_2[1].x);
    O.FastSetWithActivation(strideO * (y+2) + x+5 + offsetO], dstA_2[1].y);
    O.FastSetWithActivation(strideO * (y+2) + x+6 + offsetO], dstA_2[1].z);
    O.FastSetWithActivation(strideO * (y+2) + x+7 + offsetO], dstA_2[1].w);
    O.FastSetWithActivation(strideO * (y+3) + x+0 + offsetO], dstA_3[0].x);
    O.FastSetWithActivation(strideO * (y+3) + x+1 + offsetO], dstA_3[0].y);
    O.FastSetWithActivation(strideO * (y+3) + x+2 + offsetO], dstA_3[0].z);
    O.FastSetWithActivation(strideO * (y+3) + x+3 + offsetO], dstA_3[0].w);
    O.FastSetWithActivation(strideO * (y+3) + x+4 + offsetO], dstA_3[1].x);
    O.FastSetWithActivation(strideO * (y+3) + x+5 + offsetO], dstA_3[1].y);
    O.FastSetWithActivation(strideO * (y+3) + x+6 + offsetO], dstA_3[1].z);
    O.FastSetWithActivation(strideO * (y+3) + x+7 + offsetO], dstA_3[1].w);

    O.FastSetWithActivation(strideO * (y+4) + x+0 + offsetO], dstA_0[2].x);
    O.FastSetWithActivation(strideO * (y+4) + x+1 + offsetO], dstA_0[2].y);
    O.FastSetWithActivation(strideO * (y+4) + x+2 + offsetO], dstA_0[2].z);
    O.FastSetWithActivation(strideO * (y+4) + x+3 + offsetO], dstA_0[2].w);
    O.FastSetWithActivation(strideO * (y+4) + x+4 + offsetO], dstA_0[3].x);
    O.FastSetWithActivation(strideO * (y+4) + x+5 + offsetO], dstA_0[3].y);
    O.FastSetWithActivation(strideO * (y+4) + x+6 + offsetO], dstA_0[3].z);
    O.FastSetWithActivation(strideO * (y+4) + x+7 + offsetO], dstA_0[3].w);
    O.FastSetWithActivation(strideO * (y+5) + x+0 + offsetO], dstA_1[2].x);
    O.FastSetWithActivation(strideO * (y+5) + x+1 + offsetO], dstA_1[2].y);
    O.FastSetWithActivation(strideO * (y+5) + x+2 + offsetO], dstA_1[2].z);
    O.FastSetWithActivation(strideO * (y+5) + x+3 + offsetO], dstA_1[2].w);
    O.FastSetWithActivation(strideO * (y+5) + x+4 + offsetO], dstA_1[3].x);
    O.FastSetWithActivation(strideO * (y+5) + x+5 + offsetO], dstA_1[3].y);
    O.FastSetWithActivation(strideO * (y+5) + x+6 + offsetO], dstA_1[3].z);
    O.FastSetWithActivation(strideO * (y+5) + x+7 + offsetO], dstA_1[3].w);
    O.FastSetWithActivation(strideO * (y+6) + x+0 + offsetO], dstA_2[2].x);
    O.FastSetWithActivation(strideO * (y+6) + x+1 + offsetO], dstA_2[2].y);
    O.FastSetWithActivation(strideO * (y+6) + x+2 + offsetO], dstA_2[2].z);
    O.FastSetWithActivation(strideO * (y+6) + x+3 + offsetO], dstA_2[2].w);
    O.FastSetWithActivation(strideO * (y+6) + x+4 + offsetO], dstA_2[3].x);
    O.FastSetWithActivation(strideO * (y+6) + x+5 + offsetO], dstA_2[3].y);
    O.FastSetWithActivation(strideO * (y+6) + x+6 + offsetO], dstA_2[3].z);
    O.FastSetWithActivation(strideO * (y+6) + x+7 + offsetO], dstA_2[3].w);
    O.FastSetWithActivation(strideO * (y+7) + x+0 + offsetO], dstA_3[2].x);
    O.FastSetWithActivation(strideO * (y+7) + x+1 + offsetO], dstA_3[2].y);
    O.FastSetWithActivation(strideO * (y+7) + x+2 + offsetO], dstA_3[2].z);
    O.FastSetWithActivation(strideO * (y+7) + x+3 + offsetO], dstA_3[2].w);
    O.FastSetWithActivation(strideO * (y+7) + x+4 + offsetO], dstA_3[3].x);
    O.FastSetWithActivation(strideO * (y+7) + x+5 + offsetO], dstA_3[3].y);
    O.FastSetWithActivation(strideO * (y+7) + x+6 + offsetO], dstA_3[3].z);
    O.FastSetWithActivation(strideO * (y+7) + x+7 + offsetO], dstA_3[3].w);
#endif

    #undef X_
    #undef W_
}
#undef TRANSPOSED_X
#undef BLOCKED_W
#undef HARDCODED_DIMS
#undef BUF_OFFSET
#undef CACHE_DEPTH
#elif BLOCK_SIZE == 4
#define TRANSPOSED_X 0
#define SHIFTED_X 0
#define CACHE_DEPTH 4
groupshared float CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, X)[CACHE_DEPTH*8*BLOCK_SIZE+SHIFTED_X*CACHE_DEPTH];
groupshared float CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, W)[CACHE_DEPTH*8*BLOCK_SIZE];
[numthreads(8,8,1)]
void FUNC_NAME(KERNEL_NAME, BLOCK_SIZE)(uint3 dispatchThreadID : SV_DispatchThreadID, uint3 groupThreadID : SV_GroupThreadID, uint threadIndex : SV_GroupIndex)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    int x = (int)dispatchThreadID.x * BLOCK_SIZE;
    int y = (int)dispatchThreadID.y * BLOCK_SIZE;
    int tx = (int)groupThreadID.x;
    int ty = (int)groupThreadID.y;
    int bx = ((int)dispatchThreadID.x - (int)groupThreadID.x) * BLOCK_SIZE;
    int by = ((int)dispatchThreadID.y - (int)groupThreadID.y) * BLOCK_SIZE;
    int ti = (int)threadIndex;
    int n = (int)X.GetFlatWidth();
    int strideX = (int)X.GetFlatWidth();
    int strideW = (int)W.GetFlatWidth();

    #define X_ CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, X)
    #define W_ CACHE_NAME(KERNEL_NAME, BLOCK_SIZE, W)

    //if (x >= (int)O.GetFlatWidth()) return;
    //if (y >= (int)O.GetFlatHeight()) return;

    float4 dstA_0, dstA_1, dstA_2, dstA_3;

    dstA_0.x = B.FastGet(x+0);
    dstA_1.x = B.FastGet(x+0);
    dstA_2.x = B.FastGet(x+0);
    dstA_3.x = B.FastGet(x+0);
    dstA_0.y = B.FastGet(x+1);
    dstA_1.y = B.FastGet(x+1);
    dstA_2.y = B.FastGet(x+1);
    dstA_3.y = B.FastGet(x+1);
    dstA_0.z = B.FastGet(x+2);
    dstA_1.z = B.FastGet(x+2);
    dstA_2.z = B.FastGet(x+2);
    dstA_3.z = B.FastGet(x+2);
    dstA_0.w = B.FastGet(x+3);
    dstA_1.w = B.FastGet(x+3);
    dstA_2.w = B.FastGet(x+3);
    dstA_3.w = B.FastGet(x+3);

    for (int i = 0; i < n; i += CACHE_DEPTH)
    {
    #if CACHE_DEPTH == 16
        W_[ti    ] = W.data[strideW * (i + (ti>>5) + 0) + bx + (ti&31)];
        W_[ti+ 64] = W.data[strideW * (i + (ti>>5) + 2) + bx + (ti&31)];
        W_[ti+128] = W.data[strideW * (i + (ti>>5) + 4) + bx + (ti&31)];
        W_[ti+192] = W.data[strideW * (i + (ti>>5) + 6) + bx + (ti&31)];
        W_[ti+256] = W.data[strideW * (i + (ti>>5) + 8) + bx + (ti&31)];
        W_[ti+320] = W.data[strideW * (i + (ti>>5) +10) + bx + (ti&31)];
        W_[ti+384] = W.data[strideW * (i + (ti>>5) +12) + bx + (ti&31)];
        W_[ti+448] = W.data[strideW * (i + (ti>>5) +14) + bx + (ti&31)];
        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>5) + 0) + by + (ti&31)];
        X_[ti+ 64] = X.data[strideX * (i + (ti>>5) + 2) + by + (ti&31)];
        X_[ti+128] = X.data[strideX * (i + (ti>>5) + 4) + by + (ti&31)];
        X_[ti+192] = X.data[strideX * (i + (ti>>5) + 6) + by + (ti&31)];
        X_[ti+256] = X.data[strideX * (i + (ti>>5) + 8) + by + (ti&31)];
        X_[ti+320] = X.data[strideX * (i + (ti>>5) +10) + by + (ti&31)];
        X_[ti+384] = X.data[strideX * (i + (ti>>5) +12) + by + (ti&31)];
        X_[ti+448] = X.data[strideX * (i + (ti>>5) +14) + by + (ti&31)];
        #elif SHIFTED_X == 1
        /*
        g=ti/16
        j=ti&15

        g0 j0123456789ABCDEF
        g1 j0123456789ABCDEF
        g2 j0123456789ABCDEF
        g3 j0123456789ABCDEF
        g0.j0 g1.j0 g2.j0 g3.j0 g0.j1 g1.j1 g2.j1 g3.j1

        16x32 => 32x16
        */
        X_[(ti>>4) + 33*(ti&15) + 0] = X.data[strideX * (by + (ti>>4) + 0) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) + 4] = X.data[strideX * (by + (ti>>4) + 4) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) + 8] = X.data[strideX * (by + (ti>>4) + 8) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) +12] = X.data[strideX * (by + (ti>>4) +12) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) +16] = X.data[strideX * (by + (ti>>4) +16) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) +20] = X.data[strideX * (by + (ti>>4) +20) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) +24] = X.data[strideX * (by + (ti>>4) +24) + i + (ti&15) ];
        X_[(ti>>4) + 33*(ti&15) +28] = X.data[strideX * (by + (ti>>4) +28) + i + (ti&15) ];
        #else
        //X_[ti] = X.Get(by + (ti/16), i + (ti&15));
        X_[ti    ] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 0];
        X_[ti+ 64] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 2];
        X_[ti+128] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 4];
        X_[ti+192] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 6];
        X_[ti+256] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 8];
        X_[ti+320] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) +10];
        X_[ti+384] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) +12];
        X_[ti+448] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) +14];
        #endif

    #elif CACHE_DEPTH == 8
        W_[ti    ] = W.data[strideW * (i + (ti>>5) + 0) + bx + (ti&31)];
        W_[ti+ 64] = W.data[strideW * (i + (ti>>5) + 2) + bx + (ti&31)];
        W_[ti+128] = W.data[strideW * (i + (ti>>5) + 4) + bx + (ti&31)];
        W_[ti+192] = W.data[strideW * (i + (ti>>5) + 6) + bx + (ti&31)];
        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>5) + 0) + by + (ti&31)];
        X_[ti+ 64] = X.data[strideX * (i + (ti>>5) + 2) + by + (ti&31)];
        X_[ti+128] = X.data[strideX * (i + (ti>>5) + 4) + by + (ti&31)];
        X_[ti+192] = X.data[strideX * (i + (ti>>5) + 6) + by + (ti&31)];
        #elif SHIFTED_X == 1
        // 8x32 => 32x8
        X_[(ti>>3) + 33*(ti&7) + 0] = X.data[strideX * (by + (ti>>3) + 0) + i + (ti&7) ];
        X_[(ti>>3) + 33*(ti&7) + 8] = X.data[strideX * (by + (ti>>3) + 8) + i + (ti&7) ];
        X_[(ti>>3) + 33*(ti&7) +16] = X.data[strideX * (by + (ti>>3) +16) + i + (ti&7) ];
        X_[(ti>>3) + 33*(ti&7) +24] = X.data[strideX * (by + (ti>>3) +24) + i + (ti&7) ];
        #else
        // 8x32 => 32x8
        X_[ti    ] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 0];
        X_[ti+ 64] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 2];
        X_[ti+128] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 4];
        X_[ti+192] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 6];
        #endif

    #elif CACHE_DEPTH == 4
        W_[ti    ] = W.data[strideW * (i + (ti>>5) + 0) + bx + (ti&31)];
        W_[ti+ 64] = W.data[strideW * (i + (ti>>5) + 2) + bx + (ti&31)];
        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>5) + 0) + by + (ti&31)];
        X_[ti+ 64] = X.data[strideX * (i + (ti>>5) + 2) + by + (ti&31)];
        #elif SHIFTED_X == 1
        // 4x32 => 32x4
        X_[(ti>>2) + 33*(ti&3) + 0] = X.data[strideX * (by + (ti>>2) + 0) + i + (ti&3) ];
        X_[(ti>>2) + 33*(ti&3) +16] = X.data[strideX * (by + (ti>>2) + 16) + i + (ti&3) ];
        #else
        // 4x32 => 32x4
        X_[ti    ] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 0];
        X_[ti+ 64] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 2];
        #endif

    #elif CACHE_DEPTH == 2
        W_[ti    ] = W.data[strideW * (i + (ti>>5) + 0) + bx + (ti&31)];
        #if TRANSPOSED_X == 1
        X_[ti    ] = X.data[strideX * (i + (ti>>5) + 0) + by + (ti&31)];
        #elif SHIFTED_X == 1
        // 2x32 => 32x2
        X_[(ti>>1) + 33*(ti&1) + 0] = X.data[strideX * (by + (ti>>1) + 0) + i + (ti&1) ];
        #else
        X_[ti    ] = X.data[strideX * (by + (ti&31)) + i + (ti>>5) + 0];
        #endif

    #elif CACHE_DEPTH == 1
        if (ti < 32)
        {
            W_[ti] = W.data[strideW * i + bx + ti];
            #if TRANSPOSED_X == 1
            X_[ti] = X.data[strideX * i + by + ti];
            #else
            //X_[ti] = X.Get(by+ti, i);
            X_[ti] = X.data[strideX * (by + ti) + i];
            #endif
        }
    #endif

        GroupMemoryBarrierWithGroupSync();

        for (int di = 0; di < CACHE_DEPTH; di++)
        {
            int _32 = 32 + SHIFTED_X;
            float4 srcX = float4(
                X_[di*_32 + ty*4 + 0],
                X_[di*_32 + ty*4 + 1],
                X_[di*_32 + ty*4 + 2],
                X_[di*_32 + ty*4 + 3]);
            float4 srcW = float4(
                W_[di*32 + tx*4 + 0],
                W_[di*32 + tx*4 + 1],
                W_[di*32 + tx*4 + 2],
                W_[di*32 + tx*4 + 3]);

            dstA_0.x = ffma(srcX.x, srcW.x, dstA_0.x);
            dstA_0.y = ffma(srcX.x, srcW.y, dstA_0.y);
            dstA_0.z = ffma(srcX.x, srcW.z, dstA_0.z);
            dstA_0.w = ffma(srcX.x, srcW.w, dstA_0.w);

            dstA_1.x = ffma(srcX.y, srcW.x, dstA_1.x);
            dstA_1.y = ffma(srcX.y, srcW.y, dstA_1.y);
            dstA_1.z = ffma(srcX.y, srcW.z, dstA_1.z);
            dstA_1.w = ffma(srcX.y, srcW.w, dstA_1.w);

            dstA_2.x = ffma(srcX.z, srcW.x, dstA_2.x);
            dstA_2.y = ffma(srcX.z, srcW.y, dstA_2.y);
            dstA_2.z = ffma(srcX.z, srcW.z, dstA_2.z);
            dstA_2.w = ffma(srcX.z, srcW.w, dstA_2.w);

            dstA_3.x = ffma(srcX.w, srcW.x, dstA_3.x);
            dstA_3.y = ffma(srcX.w, srcW.y, dstA_3.y);
            dstA_3.z = ffma(srcX.w, srcW.z, dstA_3.z);
            dstA_3.w = ffma(srcX.w, srcW.w, dstA_3.w);
        }

        GroupMemoryBarrierWithGroupSync();
    }

    O.SetWithActivation(y+0, x+0, dstA_0.x);
    O.SetWithActivation(y+0, x+1, dstA_0.y);
    O.SetWithActivation(y+0, x+2, dstA_0.z);
    O.SetWithActivation(y+0, x+3, dstA_0.w);
    O.SetWithActivation(y+1, x+0, dstA_1.x);
    O.SetWithActivation(y+1, x+1, dstA_1.y);
    O.SetWithActivation(y+1, x+2, dstA_1.z);
    O.SetWithActivation(y+1, x+3, dstA_1.w);
    O.SetWithActivation(y+2, x+0, dstA_2.x);
    O.SetWithActivation(y+2, x+1, dstA_2.y);
    O.SetWithActivation(y+2, x+2, dstA_2.z);
    O.SetWithActivation(y+2, x+3, dstA_2.w);
    O.SetWithActivation(y+3, x+0, dstA_3.x);
    O.SetWithActivation(y+3, x+1, dstA_3.y);
    O.SetWithActivation(y+3, x+2, dstA_3.z);
    O.SetWithActivation(y+3, x+3, dstA_3.w);
    /*for (dx = 0; dx < BLOCK_SIZE; ++dx)
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            O.SetWithActivation(y+dy, x+dx, dstA[dy][dx]);
    */
    #undef X_
    #undef W_
}
#undef TRANSPOSED_X
#undef SHIFTED_X
#undef CACHE_DEPTH
#else
[numthreads(8,8,1)]
void FUNC_NAME(KERNEL_NAME, BLOCK_SIZE)(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    int x = (int)dispatchThreadID.x * BLOCK_SIZE;
    int y = (int)dispatchThreadID.y * BLOCK_SIZE;
    int n = (int)X.GetFlatWidth();

    if (x >= (int)O.GetFlatWidth()) return;
    if (y >= (int)O.GetFlatHeight()) return;

    float dstA[BLOCK_SIZE][BLOCK_SIZE];
    float srcX[BLOCK_SIZE];

    int dy, dx;
    for (dx = 0; dx < BLOCK_SIZE; ++dx)
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            dstA[dy][dx] = B.data[x+dx+B.offset];//B.Get(x+dx);

    for (int i = 0; i < n; ++i)
    {
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            srcX[dy] = X.data[(y+dy)*X.channels+i];//X.Get(y+dy, i);

        for (dx = 0; dx < BLOCK_SIZE; ++dx)
        {
            float srcW = W.data[i*W.channels+x+dx];//W.Get(i, x+dx);
            for (dy = 0; dy < BLOCK_SIZE; ++dy)
                dstA[dy][dx] += srcX[dy] * srcW;
        }
    }

    for (dx = 0; dx < BLOCK_SIZE; ++dx)
        for (dy = 0; dy < BLOCK_SIZE; ++dy)
            O.SetWithActivation(y+dy, x+dx, dstA[dy][dx]);
}
#endif
#undef KERNEL_NAME

#endif // DENSE

// NOTE: usually this path is used for <16 batches
#undef CACHESIZE
#define CACHESIZE 64
groupshared float Dense_L1Cached64_X[CACHESIZE];

[numthreads(CACHESIZE, 1, 1)]
void Dense_L1Cached64(uint3 groupID : SV_GroupID, uint3 groupThreadID : SV_GroupThreadID)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    #define X_ Dense_L1Cached64_X

    uint x = CACHESIZE * groupID.x + groupThreadID.x;
    uint y = groupID.y;

    uint wIndex = W.Index(0, x);

    float acc = B.FastGet(min(x, O.GetFlatWidth()-1));
    // loop over X columns (flatWidth) and W rows (height) in CACHESIZE steps
    for (uint i = 0; i < X.GetFlatWidth(); i += CACHESIZE)
    {
        // Cache X
        // coalescent reads
        X_[groupThreadID.x] = X.SafeGet(y, i + groupThreadID.x);
        GroupMemoryBarrierWithGroupSync();

        // X * W
        if (i + CACHESIZE <= X.GetFlatWidth())
        {
            [unroll]
            for (uint di = 0; di < CACHESIZE; ++di)
            {
                acc = fastfma(X_[di], W.data[wIndex], acc);
                wIndex += W.GetFlatWidth();
            }
        }
        else
        {
            // handle remainder of the line < CACHESIZE
            for (uint di = 0; i + di < X.GetFlatWidth(); ++di)
            {
                acc = fastfma(X_[di], W.data[wIndex], acc);
                wIndex += W.GetFlatWidth();
            }
        }

        GroupMemoryBarrierWithGroupSync();
    }

    // needed all threads to load matrix line, x might be out of the bounds for writing
    if (x < O.GetFlatWidth())
        O.SetWithActivation(y, x, acc);

    #undef X_
}


#undef TILE_WIDTH
#define TILE_WIDTH NUMTHREAD(16,8,8)
groupshared float DenseTiled_Xcache[TILE_WIDTH][TILE_WIDTH];
groupshared float DenseTiled_Wcache[TILE_WIDTH][TILE_WIDTH];
[numthreads(TILE_WIDTH,TILE_WIDTH,1)]
void DenseTiled16x16(uint3 groupID : SV_GroupID, uint3 groupThreadID : SV_GroupThreadID)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    #define X_ DenseTiled_Xcache
    #define W_ DenseTiled_Wcache

    uint tx = groupThreadID.x;
    uint ty = groupThreadID.y;
    uint x = groupID.x*TILE_WIDTH + tx;
    uint y = groupID.y*TILE_WIDTH + ty;

    bool mask = (x < O.GetFlatWidth() && y < O.GetFlatHeight());

    float v = B.FastGet(x);
    for (uint m = 0; m < X.GetFlatWidth()/TILE_WIDTH; ++m)
    {
        if (mask)
        {
            X_[ty][tx] = X.Get(y, m*TILE_WIDTH + tx);
            W_[ty][tx] = W.Get(m*TILE_WIDTH + ty, x);
        }
        else
        {
            X_[ty][tx] = 0;
            W_[ty][tx] = 0;
        }

        GroupMemoryBarrierWithGroupSync();

        [unroll]
        for (uint i = 0; i < TILE_WIDTH; ++i)
        {
            v = fastfma(X_[ty][i], W_[i][tx], v);
        }

        GroupMemoryBarrierWithGroupSync();
    }

    if (mask)
        O.SetWithActivation(y, x, v);

    #undef X_
    #undef W_
}

#undef TILE_WIDTH
#define TILE_WIDTH NUMTHREAD(16,8,8) // 32 crashes on MacBookPro/AMD
groupshared float DenseTiled_Xcache32[2*2][TILE_WIDTH][TILE_WIDTH];
groupshared float DenseTiled_Wcache32[2*2][TILE_WIDTH][TILE_WIDTH];
[numthreads(TILE_WIDTH,TILE_WIDTH,1)]
void DenseTiled32x32(uint3 groupID : SV_GroupID, uint3 groupThreadID : SV_GroupThreadID)
{
    //DISPATCH ARGS(O.flatWidth / 2, O.flatHeight / 2, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    #define X_ DenseTiled_Xcache32
    #define W_ DenseTiled_Wcache32

    uint tx = groupThreadID.x;
    uint ty = groupThreadID.y;
    uint x = groupID.x*TILE_WIDTH + tx;
    uint y = groupID.y*TILE_WIDTH + ty;

    float b0 = B.FastGet(x*2+0);
    float b1 = B.FastGet(x*2+1);
    float4 v = float4(b0, b1,
                      b0, b1);

    for (uint m = 0; m < X.GetFlatWidth()/(TILE_WIDTH*2);)
    {
        float x0 = X.Get(y*2+0, m*TILE_WIDTH*2 + tx*2+0);
        float x1 = X.Get(y*2+0, m*TILE_WIDTH*2 + tx*2+1);
        float x2 = X.Get(y*2+1, m*TILE_WIDTH*2 + tx*2+0);
        float x3 = X.Get(y*2+1, m*TILE_WIDTH*2 + tx*2+1);

        float w0 = W.Get(m*TILE_WIDTH*2 + ty*2+0, x*2+0);
        float w1 = W.Get(m*TILE_WIDTH*2 + ty*2+0, x*2+1);
        float w2 = W.Get(m*TILE_WIDTH*2 + ty*2+1, x*2+0);
        float w3 = W.Get(m*TILE_WIDTH*2 + ty*2+1, x*2+1);

        ++m;

        X_[0][ty][tx] = x0;
        X_[1][ty][tx] = x1;
        X_[2][ty][tx] = x2;
        X_[3][ty][tx] = x3;

        W_[0][ty][tx] = w0;
        W_[1][ty][tx] = w1;
        W_[2][ty][tx] = w2;
        W_[3][ty][tx] = w3;

        GroupMemoryBarrierWithGroupSync();

        [unroll]
        for (uint i = 0; i < TILE_WIDTH; ++i)
        {
            float4 x =
                float4(    X_[0][ty][i],
                        X_[1][ty][i],
                        X_[2][ty][i],
                        X_[3][ty][i]);
            float4 w =
                float4(    W_[0][i][tx],
                        W_[1][i][tx],
                        W_[2][i][tx],
                        W_[3][i][tx]);

            v.x = fastfma(w.x, x.x, v.x);
            v.y = fastfma(w.y, x.x, v.y);
            v.z = fastfma(w.x, x.z, v.z);
            v.w = fastfma(w.y, x.z, v.w);

            v.x = fastfma(w.z, x.y, v.x);
            v.y = fastfma(w.w, x.y, v.y);
            v.z = fastfma(w.z, x.w, v.z);
            v.w = fastfma(w.w, x.w, v.w);
        }

        GroupMemoryBarrierWithGroupSync();
    }

    O.SetWithActivation(y*2+0, x*2+0, v.x);
    O.SetWithActivation(y*2+0, x*2+1, v.y);
    O.SetWithActivation(y*2+1, x*2+0, v.z);
    O.SetWithActivation(y*2+1, x*2+1, v.w);

    #undef X_
    #undef W_
}

#undef TILE_WIDTH
#define TILE_WIDTH NUMTHREAD(16,8,8)
groupshared float DenseTiled_Xcache64[4*4][TILE_WIDTH*TILE_WIDTH];
groupshared float DenseTiled_Wcache64[4*4][TILE_WIDTH*TILE_WIDTH];
[numthreads(TILE_WIDTH,TILE_WIDTH,1)]
void DenseTiled64x64(uint3 groupID : SV_GroupID, uint3 groupThreadID : SV_GroupThreadID)
{
    //DISPATCH ARGS(O.flatWidth / 4, O.flatHeight / 4, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    #define X_ DenseTiled_Xcache64
    #define W_ DenseTiled_Wcache64

    uint tx = groupThreadID.x;
    uint ty = groupThreadID.y;
    uint x = groupID.x*TILE_WIDTH + tx;
    uint y = groupID.y*TILE_WIDTH + ty;

    float b0 = B.FastGet(x*4+0);
    float b1 = B.FastGet(x*4+1);
    float b2 = B.FastGet(x*4+2);
    float b3 = B.FastGet(x*4+3);

    float4 v0, v1, v2, v3;
    v0 = v1 = v2 = v3 = float4(b0, b1, b2, b3);

    for (uint m = 0; m < X.GetFlatWidth()/(TILE_WIDTH*4); ++m)
    {
        for (uint yy = 0; yy < 4; ++yy)
            for (uint xx = 0; xx < 4; ++xx)
            {
                X_[yy*4+xx][ty*TILE_WIDTH+tx] = X.Get(y*4+yy, (m*TILE_WIDTH + tx)*4+xx);
                W_[yy*4+xx][ty*TILE_WIDTH+tx] = W.Get((m*TILE_WIDTH + ty)*4+yy, x*4+xx);
            }

        GroupMemoryBarrierWithGroupSync();

        for (uint i = 0; i < TILE_WIDTH; ++i)
        {
            [unroll]
            for (uint q = 0; q < 4; ++q)
            {
                float x0 = X_[0*4+q][ty*TILE_WIDTH+i];
                float x1 = X_[1*4+q][ty*TILE_WIDTH+i];
                float x2 = X_[2*4+q][ty*TILE_WIDTH+i];
                float x3 = X_[3*4+q][ty*TILE_WIDTH+i];

                float w0 = W_[q*4+0][i*TILE_WIDTH+tx];
                float w1 = W_[q*4+1][i*TILE_WIDTH+tx];
                float w2 = W_[q*4+2][i*TILE_WIDTH+tx];
                float w3 = W_[q*4+3][i*TILE_WIDTH+tx];

                v0.x = fastfma(x0, w0, v0.x); //--
                v1.x = fastfma(x1, w0, v1.x);
                v2.x = fastfma(x2, w0, v2.x);
                v3.x = fastfma(x3, w0, v3.x);
                v0.y = fastfma(x0, w1, v0.y); //--
                v1.y = fastfma(x1, w1, v1.y);
                v2.y = fastfma(x2, w1, v2.y);
                v3.y = fastfma(x3, w1, v3.y);
                v0.z = fastfma(x0, w2, v0.z); //--
                v1.z = fastfma(x1, w2, v1.z);
                v2.z = fastfma(x2, w2, v2.z);
                v3.z = fastfma(x3, w2, v3.z);
                v0.w = fastfma(x0, w3, v0.w); //--
                v1.w = fastfma(x1, w3, v1.w);
                v2.w = fastfma(x2, w3, v2.w);
                v3.w = fastfma(x3, w3, v3.w);
            }

            GroupMemoryBarrierWithGroupSync();
        }
    }

    O.SetWithActivation(y*4+0, x*4+0, v0.x);
    O.SetWithActivation(y*4+0, x*4+1, v0.y);
    O.SetWithActivation(y*4+0, x*4+2, v0.z);
    O.SetWithActivation(y*4+0, x*4+3, v0.w);

    O.SetWithActivation(y*4+1, x*4+0, v1.x);
    O.SetWithActivation(y*4+1, x*4+1, v1.y);
    O.SetWithActivation(y*4+1, x*4+2, v1.z);
    O.SetWithActivation(y*4+1, x*4+3, v1.w);

    O.SetWithActivation(y*4+2, x*4+0, v2.x);
    O.SetWithActivation(y*4+2, x*4+1, v2.y);
    O.SetWithActivation(y*4+2, x*4+2, v2.z);
    O.SetWithActivation(y*4+2, x*4+3, v2.w);

    O.SetWithActivation(y*4+3, x*4+0, v3.x);
    O.SetWithActivation(y*4+3, x*4+1, v3.y);
    O.SetWithActivation(y*4+3, x*4+2, v3.z);
    O.SetWithActivation(y*4+3, x*4+3, v3.w);

    #undef X_
    #undef W_
}

// reference: "Optimizing the General Matrix Multiplication" GPU - Pro 5
// https://github.com/strin/gemm-android
// https://github.com/dividiti/gemmbench/tree/master/program/arm-mali-sgemm
// Best configurations one OnePlus 6T:
// K16, numthreads32x2/4/8 // K32 numthreads32x2/4/8
#undef TILE_WIDTH
#define TILE_WIDTH 16

[numthreads(32, 4, 1)]
void Dense_Tilled2x2_Cached(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.flatWidth / 2, O.flatHeight / 2, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    uint x = dispatchThreadID.x;
    uint y = dispatchThreadID.y;

    uint flatHeightO = O.GetFlatHeight();
    uint flatWidthO  = O.GetFlatWidth();
    uint flatWidthX = X.GetFlatWidth();

    float b0 = B.FastGet(min(x * 2 + 0, flatWidthO-1));
    float b1 = B.FastGet(min(x * 2 + 1, flatWidthO-1));
    float4 v = float4(b0, b1, b0, b1);

    for (uint m = 0; m < (flatWidthX + 4 - 1) / 4; m += TILE_WIDTH)
    {
        for (uint i = 0; i < TILE_WIDTH; ++i)
        {
            uint k = m + i;
            float4 x0 = float4(X.SafeGet(2 * y + 0, 4 * k + 0), X.SafeGet(2 * y + 0, 4 * k + 1), X.SafeGet(2 * y + 0, 4 * k + 2), X.SafeGet(2 * y + 0, 4 * k + 3));
            float4 x1 = float4(X.SafeGet(2 * y + 1, 4 * k + 0), X.SafeGet(2 * y + 1, 4 * k + 1), X.SafeGet(2 * y + 1, 4 * k + 2), X.SafeGet(2 * y + 1, 4 * k + 3));

            float4 w0 = float4(W.SafeGet(4 * k + 0, 2 * x + 0), W.SafeGet(4 * k + 1, 2 * x + 0), W.SafeGet(4 * k + 2, 2 * x + 0), W.SafeGet(4 * k + 3, 2 * x + 0));
            float4 w1 = float4(W.SafeGet(4 * k + 0, 2 * x + 1), W.SafeGet(4 * k + 1, 2 * x + 1), W.SafeGet(4 * k + 2, 2 * x + 1), W.SafeGet(4 * k + 3, 2 * x + 1));

            v += float4(dot(x0, w0), dot(x0, w1), dot(x1, w0), dot(x1, w1));
        }
        AllMemoryBarrierWithGroupSync();
    }

    if ((y * 2 + 0 ) < flatHeightO && (x * 2 + 0) < flatWidthO)
        O.SetWithActivation(y * 2 + 0, x * 2 + 0, v.x);
    if ((y * 2 + 0) < flatHeightO && (x * 2 + 1) < flatWidthO)
        O.SetWithActivation(y * 2 + 0, x * 2 + 1, v.y);
    if ((y * 2 + 1) < flatHeightO && (x * 2 + 0) < flatWidthO)
        O.SetWithActivation(y * 2 + 1, x * 2 + 0, v.z);
    if ((y * 2 + 1) < flatHeightO && (x * 2 + 1) < flatWidthO)
        O.SetWithActivation(y * 2 + 1, x * 2 + 1, v.w);
}

#undef TILE_WIDTH
#define TILE_WIDTH 16

[numthreads(32, 4, 1)]
void Dense_Tilled4x4_Cached(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    //DISPATCH ARGS(O.flatWidth / 4, O.flatHeight / 4, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

    uint x = dispatchThreadID.x;
    uint y = dispatchThreadID.y;

    uint flatHeightO = O.GetFlatHeight();
    uint flatWidthO = O.GetFlatWidth();
    uint flatWidthX = X.GetFlatWidth();

    float b0 = B.FastGet(min(x * 4 + 0, flatWidthO - 1));
    float b1 = B.FastGet(min(x * 4 + 1, flatWidthO - 1));
    float b2 = B.FastGet(min(x * 4 + 2, flatWidthO - 1));
    float b3 = B.FastGet(min(x * 4 + 3, flatWidthO - 1));

    float4 v0 = float4(b0, b1, b0, b1);
    float4 v1 = float4(b2, b3, b2, b3);
    float4 v2 = float4(b0, b1, b0, b1);
    float4 v3 = float4(b2, b3, b2, b3);

    for (uint m = 0; m < (flatWidthX + 4 - 1) / 4; m += TILE_WIDTH)
    {
        for (uint i = 0; i < TILE_WIDTH; ++i)
        {
            uint k = m + i;
            float4 x0 = float4(X.SafeGet(4 * y + 0, 4 * k + 0), X.SafeGet(4 * y + 0, 4 * k + 1), X.SafeGet(4 * y + 0, 4 * k + 2), X.SafeGet(4 * y + 0, 4 * k + 3));
            float4 x1 = float4(X.SafeGet(4 * y + 1, 4 * k + 0), X.SafeGet(4 * y + 1, 4 * k + 1), X.SafeGet(4 * y + 1, 4 * k + 2), X.SafeGet(4 * y + 1, 4 * k + 3));
            float4 x2 = float4(X.SafeGet(4 * y + 2, 4 * k + 0), X.SafeGet(4 * y + 2, 4 * k + 1), X.SafeGet(4 * y + 2, 4 * k + 2), X.SafeGet(4 * y + 2, 4 * k + 3));
            float4 x3 = float4(X.SafeGet(4 * y + 3, 4 * k + 0), X.SafeGet(4 * y + 3, 4 * k + 1), X.SafeGet(4 * y + 3, 4 * k + 2), X.SafeGet(4 * y + 3, 4 * k + 3));

            float4 w0 = float4(W.SafeGet(4 * k + 0, 4 * x + 0), W.SafeGet(4 * k + 1, 4 * x + 0), W.SafeGet(4 * k + 2, 4 * x + 0), W.SafeGet(4 * k + 3, 4 * x + 0));
            float4 w1 = float4(W.SafeGet(4 * k + 0, 4 * x + 1), W.SafeGet(4 * k + 1, 4 * x + 1), W.SafeGet(4 * k + 2, 4 * x + 1), W.SafeGet(4 * k + 3, 4 * x + 1));
            float4 w2 = float4(W.SafeGet(4 * k + 0, 4 * x + 2), W.SafeGet(4 * k + 1, 4 * x + 2), W.SafeGet(4 * k + 2, 4 * x + 2), W.SafeGet(4 * k + 3, 4 * x + 2));
            float4 w3 = float4(W.SafeGet(4 * k + 0, 4 * x + 3), W.SafeGet(4 * k + 1, 4 * x + 3), W.SafeGet(4 * k + 2, 4 * x + 3), W.SafeGet(4 * k + 3, 4 * x + 3));

            v0 += float4(dot(x0, w0), dot(x0, w1), dot(x1, w0), dot(x1, w1));
            v1 += float4(dot(x0, w2), dot(x0, w3), dot(x1, w2), dot(x1, w3));
            v2 += float4(dot(x2, w0), dot(x2, w1), dot(x3, w0), dot(x3, w1));
            v3 += float4(dot(x2, w2), dot(x2, w3), dot(x3, w2), dot(x3, w3));
        }
        AllMemoryBarrierWithGroupSync();
    }

    if ((y * 4 + 0) < flatHeightO && (x * 4 + 0) < flatWidthO)
        O.SetWithActivation(y * 4 + 0, x * 4 + 0, v0.x);
    if ((y * 4 + 0) < flatHeightO && (x * 4 + 1) < flatWidthO)
        O.SetWithActivation(y * 4 + 0, x * 4 + 1, v0.y);
    if ((y * 4 + 1) < flatHeightO && (x * 4 + 0) < flatWidthO)
        O.SetWithActivation(y * 4 + 1, x * 4 + 0, v0.z);
    if ((y * 4 + 1) < flatHeightO && (x * 4 + 1) < flatWidthO)
        O.SetWithActivation(y * 4 + 1, x * 4 + 1, v0.w);

    if ((y * 4 + 0) < flatHeightO && (x * 4 + 2) < flatWidthO)
        O.SetWithActivation(y * 4 + 0, x * 4 + 2, v1.x);
    if ((y * 4 + 0) < flatHeightO && (x * 4 + 3) < flatWidthO)
        O.SetWithActivation(y * 4 + 0, x * 4 + 3, v1.y);
    if ((y * 4 + 1) < flatHeightO && (x * 4 + 2) < flatWidthO)
        O.SetWithActivation(y * 4 + 1, x * 4 + 2, v1.z);
    if ((y * 4 + 1) < flatHeightO && (x * 4 + 3) < flatWidthO)
        O.SetWithActivation(y * 4 + 1, x * 4 + 3, v1.w);

    if ((y * 4 + 2) < flatHeightO && (x * 4 + 0) < flatWidthO)
        O.SetWithActivation(y * 4 + 2, x * 4 + 0, v2.x);
    if ((y * 4 + 2) < flatHeightO && (x * 4 + 1) < flatWidthO)
        O.SetWithActivation(y * 4 + 2, x * 4 + 1, v2.y);
    if ((y * 4 + 3) < flatHeightO && (x * 4 + 0) < flatWidthO)
        O.SetWithActivation(y * 4 + 3, x * 4 + 0, v2.z);
    if ((y * 4 + 3) < flatHeightO && (x * 4 + 1) < flatWidthO)
        O.SetWithActivation(y * 4 + 3, x * 4 + 1, v2.w);

    if ((y * 4 + 2) < flatHeightO && (x * 4 + 2) < flatWidthO)
        O.SetWithActivation(y * 4 + 2, x * 4 + 2, v3.x);
    if ((y * 4 + 2) < flatHeightO && (x * 4 + 3) < flatWidthO)
        O.SetWithActivation(y * 4 + 2, x * 4 + 3, v3.y);
    if ((y * 4 + 3) < flatHeightO && (x * 4 + 2) < flatWidthO)
        O.SetWithActivation(y * 4 + 3, x * 4 + 2, v3.z);
    if ((y * 4 + 3) < flatHeightO && (x * 4 + 3) < flatWidthO)
        O.SetWithActivation(y * 4 + 3, x * 4 + 3, v3.w);
}


[numthreads(8, 8, 1)]
void MatMulPackB0Bias(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    TENSOR_ARGS2(X, O);

    uint x = dispatchThreadID.x;
    uint y = dispatchThreadID.y;

    if (x >= O.GetFlatWidth()) return;
    if (y >= O.GetFlatHeight()) return;

    float v = X.Get(y, x);
    O.Set(y, x, v);
    O.FastSet(O.GetLength() + x, 0.0f);
}



#undef CACHESIZE
#undef LDS_
#undef X_OFFSET
#undef W_OFFSET
#define CACHESIZE 64
groupshared float Dense_V_L1Cached64_LDS[CACHESIZE];

[numthreads(64, 1, 1)]
void Dense_V_L1Cached64(uint3 groupID : SV_GroupID, uint threadIndex : SV_GroupIndex, uint3 groupThreadID : SV_GroupThreadID)
{
    //DISPATCH ARGS(O.flatWidth, O.flatHeight, 1);
    TENSOR_SHARED2_ARGS4(X, W, B, WBK, O);

#define LDS_ Dense_V_L1Cached64_LDS

    uint ti = threadIndex;

    uint bx = CACHESIZE * groupID.x + ti;

    float dstO = B.FastGet(min(bx, O.GetFlatWidth() - 1));

    // loop over X columns (flatWidth) and W rows (height) in CACHESIZE steps
    for (uint i = 0; i < X.GetFlatWidth(); i += CACHESIZE)
    {
        // Cache X
        // coalescent reads
        LDS_[ti] = X.FastGet(min(i + ti, X.GetFlatWidth() - 1));

        GroupMemoryBarrierWithGroupSync();

        // X * W
        [unroll]
        for (uint di = 0; di < CACHESIZE; ++di)
        {
            dstO = fastfma(LDS_[di], W.SafeGet(bx + (i + di)*W.GetFlatWidth()), dstO);
        }

        GroupMemoryBarrierWithGroupSync();
    }

    if(bx < O.GetFlatWidth())
        O.FastSetWithActivation(bx, dstO);

#undef LDS_
}
