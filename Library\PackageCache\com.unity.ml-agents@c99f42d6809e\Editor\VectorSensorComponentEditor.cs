using UnityEditor;
using Unity.MLAgents.Sensors;

namespace Unity.MLAgents.Editor
{
    [CustomEditor(typeof(VectorSensorComponent), editorForChildClasses: true)]
    [CanEditMultipleObjects]
    internal class VectorSensorComponentEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var so = serializedObject;
            so.Update();

            // Drawing the VectorSensorComponent

            EditorGUI.BeginDisabledGroup(!EditorUtilities.CanUpdateModelProperties());
            {
                // These fields affect the sensor order or observation size,
                // So can't be changed at runtime.
                EditorGUILayout.PropertyField(so.FindProperty("m_SensorName"), true);
                EditorGUILayout.PropertyField(so.FindProperty("m_ObservationSize"), true);
                EditorGUILayout.PropertyField(so.FindProperty("m_ObservationType"), true);
            }
            EditorGUI.EndDisabledGroup();

            so.ApplyModifiedProperties();
        }
    }
}
