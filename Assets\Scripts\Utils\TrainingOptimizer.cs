using UnityEngine;
using Unity.MLAgents.Policies;

/// <summary>
/// Automatically optimizes training performance and provides real-time adjustments
/// </summary>
public class TrainingOptimizer : MonoBehaviour
{
    [Header("Performance Settings")]
    public bool autoOptimize = true;
    public float targetFPS = 60f;
    public float maxTimeScale = 20f;
    public float minTimeScale = 1f;
    
    [Header("Quality Optimization")]
    public bool dynamicQuality = true;
    public int trainingQualityLevel = 0;
    public int normalQualityLevel = 5;
    
    [Header("Training Metrics")]
    public float currentFPS;
    public float averageFPS;
    public float optimalTimeScale;
    public bool isTrainingMode;
    
    private float fpsUpdateInterval = 1f;
    private float lastFPSUpdate;
    private int frameCount;
    private float fpsSum;
    private SquadMateAgent agent;
    private BehaviorParameters behaviorParams;
    
    void Start()
    {
        agent = FindObjectOfType<SquadMateAgent>();
        if (agent != null)
            behaviorParams = agent.GetComponent<BehaviorParameters>();
        
        // Set initial training optimizations
        if (autoOptimize)
        {
            OptimizeForTraining();
        }
        
        Debug.Log("🚀 Training Optimizer Started");
        Debug.Log($"Target FPS: {targetFPS}, Max Time Scale: {maxTimeScale}");
    }
    
    void Update()
    {
        UpdateFPSTracking();
        
        if (autoOptimize && Time.time - lastFPSUpdate > fpsUpdateInterval)
        {
            OptimizePerformance();
            lastFPSUpdate = Time.time;
        }
        
        // Check if we're in training mode
        CheckTrainingMode();
    }
    
    void UpdateFPSTracking()
    {
        frameCount++;
        currentFPS = 1f / Time.unscaledDeltaTime;
        fpsSum += currentFPS;
        
        if (Time.time - lastFPSUpdate > fpsUpdateInterval)
        {
            averageFPS = fpsSum / frameCount;
            frameCount = 0;
            fpsSum = 0f;
        }
    }
    
    void OptimizePerformance()
    {
        if (!autoOptimize) return;
        
        // Adjust time scale based on FPS
        if (averageFPS > targetFPS * 1.2f)
        {
            // FPS is good, can increase time scale
            float newTimeScale = Mathf.Min(Time.timeScale * 1.1f, maxTimeScale);
            Time.timeScale = newTimeScale;
            optimalTimeScale = newTimeScale;
        }
        else if (averageFPS < targetFPS * 0.8f)
        {
            // FPS is low, decrease time scale
            float newTimeScale = Mathf.Max(Time.timeScale * 0.9f, minTimeScale);
            Time.timeScale = newTimeScale;
            optimalTimeScale = newTimeScale;
        }
        
        // Adjust quality if needed
        if (dynamicQuality)
        {
            if (averageFPS < targetFPS * 0.6f && QualitySettings.GetQualityLevel() > 0)
            {
                QualitySettings.DecreaseLevel();
                Debug.Log($"🔧 Decreased quality level to {QualitySettings.GetQualityLevel()}");
            }
            else if (averageFPS > targetFPS * 1.5f && QualitySettings.GetQualityLevel() < 5)
            {
                QualitySettings.IncreaseLevel();
                Debug.Log($"🔧 Increased quality level to {QualitySettings.GetQualityLevel()}");
            }
        }
    }
    
    void CheckTrainingMode()
    {
        bool wasTrainingMode = isTrainingMode;
        
        if (behaviorParams != null)
        {
            isTrainingMode = behaviorParams.BehaviorType == BehaviorType.Default;
        }
        
        // If training mode changed, optimize accordingly
        if (isTrainingMode != wasTrainingMode)
        {
            if (isTrainingMode)
            {
                OptimizeForTraining();
                Debug.Log("🧠 Switched to Training Mode - Optimizing...");
            }
            else
            {
                OptimizeForInference();
                Debug.Log("🤖 Switched to Inference Mode - Restoring quality...");
            }
        }
    }
    
    public void OptimizeForTraining()
    {
        // Set low quality for training
        if (dynamicQuality)
        {
            QualitySettings.SetQualityLevel(trainingQualityLevel);
        }
        
        // Disable unnecessary features
        Application.targetFrameRate = -1; // Unlimited FPS
        
        // Set optimal time scale
        Time.timeScale = Mathf.Min(maxTimeScale, 15f);
        
        Debug.Log("🎯 Optimized for Training Mode");
    }
    
    public void OptimizeForInference()
    {
        // Restore normal quality
        if (dynamicQuality)
        {
            QualitySettings.SetQualityLevel(normalQualityLevel);
        }
        
        // Normal frame rate
        Application.targetFrameRate = 60;
        
        // Normal time scale
        Time.timeScale = 1f;
        
        Debug.Log("🎮 Optimized for Inference Mode");
    }
    
    // Manual controls
    [ContextMenu("Force Training Optimization")]
    public void ForceTrainingOptimization()
    {
        OptimizeForTraining();
    }
    
    [ContextMenu("Force Inference Optimization")]
    public void ForceInferenceOptimization()
    {
        OptimizeForInference();
    }
    
    [ContextMenu("Reset Time Scale")]
    public void ResetTimeScale()
    {
        Time.timeScale = 1f;
        Debug.Log("⏰ Time scale reset to 1x");
    }
    
    void OnGUI()
    {
        if (!autoOptimize) return;
        
        // Performance overlay (top-right corner)
        float panelWidth = 200f;
        float panelHeight = 120f;
        float margin = 15f;
        
        GUILayout.BeginArea(new Rect(Screen.width - panelWidth - margin, margin, panelWidth, panelHeight));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("⚡ Performance", EditorStyles.boldLabel);
        GUILayout.Label($"FPS: {currentFPS:F0} (avg: {averageFPS:F0})");
        GUILayout.Label($"Time Scale: {Time.timeScale:F1}x");
        GUILayout.Label($"Quality: {QualitySettings.GetQualityLevel()}");
        GUILayout.Label($"Mode: {(isTrainingMode ? "Training" : "Inference")}");
        
        // Quick controls
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("1x"))
            Time.timeScale = 1f;
        if (GUILayout.Button("5x"))
            Time.timeScale = 5f;
        if (GUILayout.Button("10x"))
            Time.timeScale = 10f;
        if (GUILayout.Button("20x"))
            Time.timeScale = 20f;
        GUILayout.EndHorizontal();
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
